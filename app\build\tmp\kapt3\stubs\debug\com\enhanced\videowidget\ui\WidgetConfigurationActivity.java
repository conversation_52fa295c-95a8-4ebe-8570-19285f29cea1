package com.enhanced.videowidget.ui;

import java.lang.System;

/**
 * Configuration activity for setting up new video widgets
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0015\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\f\u0018\u0000 32\u00020\u0001:\u00013B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0013\u001a\u00020\u0014H\u0002J\b\u0010\u0015\u001a\u00020\u0014H\u0002J\b\u0010\u0016\u001a\u00020\u0014H\u0002J\u0012\u0010\u0017\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0018\u001a\u00020\rH\u0002J\u0010\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\rH\u0002J\"\u0010\u001b\u001a\u00020\u00142\u0006\u0010\u001c\u001a\u00020\u00042\u0006\u0010\u001d\u001a\u00020\u00042\b\u0010\u001e\u001a\u0004\u0018\u00010\u0011H\u0014J\u0012\u0010\u001f\u001a\u00020\u00142\b\u0010 \u001a\u0004\u0018\u00010!H\u0014J-\u0010\"\u001a\u00020\u00142\u0006\u0010\u001c\u001a\u00020\u00042\u000e\u0010#\u001a\n\u0012\u0006\b\u0001\u0012\u00020\b0\u00072\u0006\u0010$\u001a\u00020%H\u0016\u00a2\u0006\u0002\u0010&J\b\u0010\'\u001a\u00020(H\u0016J\b\u0010)\u001a\u00020\u0014H\u0002J\b\u0010*\u001a\u00020\u0014H\u0002J\b\u0010+\u001a\u00020\u0014H\u0002J\b\u0010,\u001a\u00020\u0014H\u0002J\u0010\u0010-\u001a\u00020\u00142\u0006\u0010.\u001a\u00020\bH\u0002J\b\u0010/\u001a\u00020\u0014H\u0002J\b\u00100\u001a\u00020\u0014H\u0002J\u001a\u00101\u001a\u00020\u00142\u0006\u00102\u001a\u00020(2\b\b\u0002\u0010.\u001a\u00020\bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0010\u001a\u0010\u0012\f\u0012\n \u0012*\u0004\u0018\u00010\u00110\u00110\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/enhanced/videowidget/ui/WidgetConfigurationActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "appWidgetId", "", "permissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "", "repository", "Lcom/enhanced/videowidget/data/repository/WidgetRepository;", "selectedVideoPath", "selectedVideoUri", "Landroid/net/Uri;", "videoFileManager", "Lcom/enhanced/videowidget/utils/VideoFileManager;", "videoPickerLauncher", "Landroid/content/Intent;", "kotlin.jvm.PlatformType", "checkPermissionsAndOpenPicker", "", "createDebugWidget", "createWidget", "getVideoPath", "uri", "handleVideoSelection", "videoUri", "onActivityResult", "requestCode", "resultCode", "data", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onRequestPermissionsResult", "permissions", "grantResults", "", "(I[Ljava/lang/String;[I)V", "onSupportNavigateUp", "", "openVideoPicker", "requestBatteryOptimizationExemption", "setupRepository", "setupUI", "showError", "message", "showPermissionExplanation", "testWithSampleVideo", "updateProgress", "isVisible", "Companion", "app_debug"})
public final class WidgetConfigurationActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.ui.WidgetConfigurationActivity.Companion Companion = null;
    private com.enhanced.videowidget.data.repository.WidgetRepository repository;
    private com.enhanced.videowidget.utils.VideoFileManager videoFileManager;
    private int appWidgetId = android.appwidget.AppWidgetManager.INVALID_APPWIDGET_ID;
    private android.net.Uri selectedVideoUri;
    private java.lang.String selectedVideoPath;
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> videoPickerLauncher = null;
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> permissionLauncher = null;
    
    public WidgetConfigurationActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupRepository() {
    }
    
    private final void setupUI() {
    }
    
    private final void checkPermissionsAndOpenPicker() {
    }
    
    private final void showPermissionExplanation() {
    }
    
    private final void requestBatteryOptimizationExemption() {
    }
    
    private final void openVideoPicker() {
    }
    
    private final void handleVideoSelection(android.net.Uri videoUri) {
    }
    
    private final java.lang.String getVideoPath(android.net.Uri uri) {
        return null;
    }
    
    private final void createWidget() {
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void updateProgress(boolean isVisible, java.lang.String message) {
    }
    
    private final void testWithSampleVideo() {
    }
    
    private final void createDebugWidget() {
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
    
    @java.lang.Override
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull
    int[] grantResults) {
    }
    
    @java.lang.Override
    protected void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable
    android.content.Intent data) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/enhanced/videowidget/ui/WidgetConfigurationActivity$Companion;", "", "()V", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}