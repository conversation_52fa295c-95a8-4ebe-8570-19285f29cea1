<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <attr format="dimension" name="appWidgetInnerRadius">
    </attr>
    <attr format="dimension" name="appWidgetPadding">
    </attr>
    <attr format="dimension" name="appWidgetRadius">
    </attr>
    <color name="background">#f6f6f6</color>
    <color name="backgroundSecondary">#ffffff</color>
    <color name="black">#FF000000</color>
    <color name="blue">#1885f2</color>
    <color name="blue_light">#b0d5f2</color>
    <color name="btn_disabled_color">#e7e7e7</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="textColor">#252525</color>
    <color name="textColorSecondary">#6b6b7a</color>
    <color name="text_disabled_color">#bfbfbf</color>
    <color name="white">#FFFFFFFF</color>
    <string name="about">About</string>
    <string name="action_delete">Delete</string>
    <string name="action_edit">Edit</string>
    <string name="action_pause">Pause</string>
    <string name="action_play">Play</string>
    <string name="action_settings">Settings</string>
    <string name="add_widget">Add Widget</string>
    <string name="app_name">Enhanced Video Widget</string>
    <string name="auto">Auto</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_widget">Cancel</string>
    <string name="create_widget">Create a Widget by long pressing the app icon</string>
    <string name="dark">Dark</string>
    <string name="default_widget_title">Video Widget</string>
    <string name="enable_notifications">Enable Notifications</string>
    <string name="error_file_not_found">Video file not found</string>
    <string name="error_unsupported_format">Unsupported video format</string>
    <string name="error_video_processing">Error processing video</string>
    <string name="error_widget_creation">Error creating widget</string>
    <string name="general_settings">General settings</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="light">Light</string>
    <string name="load_widget">Loading…</string>
    <string name="loading_status">Loading...</string>
    <string name="main_activity_title">Enhanced Video Widgets</string>
    <string name="my_widgets">My Widgets</string>
    <string name="no_video_selected">No video selected</string>
    <string name="no_widgets">No widgets created yet</string>
    <string name="notifications">Notifications</string>
    <string name="paused_status">Paused</string>
    <string name="permission_denied">Permission denied</string>
    <string name="permission_granted">Permission granted</string>
    <string name="permission_settings_required">Please enable video access permission in Settings to select videos</string>
    <string name="permission_storage_message">This app needs storage permission to access your videos.</string>
    <string name="permission_storage_title">Storage Permission Required</string>
    <string name="permission_video_required">Video access permission is required to select videos</string>
    <string name="play_pause_button_description">Play or pause video</string>
    <string name="playing_status">Playing</string>
    <string name="rate">Rate</string>
    <string name="save_widget">Save</string>
    <string name="select_video">Select Video</string>
    <string name="select_video_prompt">Please select a video</string>
    <string name="settings">Settings</string>
    <string name="settings_button_description">Widget settings</string>
    <string name="share">Share</string>
    <string name="theme">Theme</string>
    <string name="upload_video">Upload a Video</string>
    <string name="video_library_access">Access Video</string>
    <string name="video_message">Access Video for Widget</string>
    <string name="video_picker_error">Unable to open video picker</string>
    <string name="video_selected">Video selected</string>
    <string name="video_widget_content_description">Video widget display</string>
    <string name="video_widgets">Video Widgets</string>
    <string name="widget_configuration_title">Configure Video Widget</string>
    <string name="widget_created">Widget created successfully</string>
    <string name="widget_deleted">Widget deleted</string>
    <string name="widget_list">Widget List</string>
    <string name="widget_provider_description">Display videos with play/pause controls on your home screen</string>
    <string name="widget_provider_label">Enhanced Video Widget</string>
    <string name="widget_settings">Widget Settings</string>
    <style name="Theme.EnhancedVideoWidget" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="Theme.EnhancedVideoWidget.AppWidgetContainer" parent="android:Theme">
        
        <item name="appWidgetRadius">16dp</item>
        
        <item name="appWidgetInnerRadius">8dp</item>
    </style>
    <style name="Theme.EnhancedVideoWidget.AppWidgetContainerParent" parent="@android:style/Theme.DeviceDefault">
        
        <item name="appWidgetRadius">16dp</item>
        
        <item name="appWidgetInnerRadius">8dp</item>
    </style>
    <style name="Theme.EnhancedVideoWidget.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>