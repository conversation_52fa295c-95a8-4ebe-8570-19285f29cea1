package com.enhanced.videowidget.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import android.widget.RemoteViews
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequest
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkInfo
import androidx.work.WorkManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.service.WidgetUpdateService
import com.enhanced.videowidget.utils.AppLogger
import com.enhanced.videowidget.worker.WidgetUpdateWorker

/**
 * Enhanced Video Widget Provider with pause/play functionality
 */
class EnhancedVideoWidget : AppWidgetProvider() {
    
    companion object {
        private const val TAG = "EnhancedVideoWidget"
        const val ACTION_WIDGET_TAP = "com.enhanced.videowidget.ACTION_WIDGET_TAP"
        const val ACTION_PLAY_PAUSE = "com.enhanced.videowidget.ACTION_PLAY_PAUSE"
        const val ACTION_SETTINGS = "com.enhanced.videowidget.ACTION_SETTINGS"
        const val EXTRA_WIDGET_ID = "widget_id"
        
        fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int,
            widget: Widget? = null,
            frameBitmap: Bitmap? = null
        ) {
            val repository = WidgetRepository.getInstance()
            val widgetData = widget ?: repository.getWidget(appWidgetId)
            
            val views = RemoteViews(context.packageName, R.layout.enhanced_video_widget)
            
            // Update video/image display
            if (frameBitmap != null) {
                views.setImageViewBitmap(R.id.imageVideoView, frameBitmap)
                views.setViewVisibility(R.id.progressBar, android.view.View.GONE)
            } else {
                // Handle paused state - show current frame or blank widget based on setting
                widgetData?.let { w ->
                    if (w.isPaused && w.showPausedFrame && w.totalFrames > 0) {
                        // Widget is paused and should show current frame
                        try {
                            val currentFrameBitmap = repository.loadFrameBitmapSync(w.id, w.currentFrameIndex)
                            if (currentFrameBitmap != null) {
                                views.setImageViewBitmap(R.id.imageVideoView, currentFrameBitmap)
                                views.setViewVisibility(R.id.progressBar, android.view.View.GONE)
                            } else {
                                // Fallback to progress bar if frame can't be loaded
                                views.setViewVisibility(R.id.progressBar, android.view.View.VISIBLE)
                            }
                        } catch (e: Exception) {
                            AppLogger.w(AppLogger.TAG_WIDGET, "Failed to load paused frame for widget ${w.id}: ${e.message}")
                            // Fallback to progress bar on error
                            views.setViewVisibility(R.id.progressBar, android.view.View.VISIBLE)
                        }
                    } else {
                        // Show progress bar (blank widget) when not paused or setting disabled
                        views.setViewVisibility(R.id.progressBar, android.view.View.VISIBLE)
                    }
                } ?: run {
                    views.setViewVisibility(R.id.progressBar, android.view.View.VISIBLE)
                }
            }
            
            // Update widget info
            widgetData?.let { w ->
                views.setTextViewText(R.id.widgetTitle, w.name)
                views.setTextViewText(R.id.widgetStatus, 
                    if (w.isPaused) context.getString(R.string.paused_status) 
                    else context.getString(R.string.playing_status)
                )
                
                // Update play/pause button
                val playPauseIcon = if (w.isPaused) R.drawable.ic_play else R.drawable.ic_pause
                views.setImageViewResource(R.id.playPauseButton, playPauseIcon)
                
                // Update frame counter (for debugging)
                views.setTextViewText(R.id.frameCounter, "${w.currentFrameIndex}/${w.totalFrames}")
            }
            
            // Set up click listeners
            setupClickListeners(context, views, appWidgetId)
            
            // Update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
        
        private fun setupClickListeners(context: Context, views: RemoteViews, appWidgetId: Int) {
            // Play/Pause button click
            val playPauseIntent = Intent(context, EnhancedVideoWidget::class.java).apply {
                action = ACTION_PLAY_PAUSE
                putExtra(EXTRA_WIDGET_ID, appWidgetId)
            }
            val playPausePendingIntent = PendingIntent.getBroadcast(
                context, 
                appWidgetId * 10 + 1, 
                playPauseIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.playPauseButton, playPausePendingIntent)
            
            // Settings button click - open VideoWidgetManagerActivity
            val settingsIntent = Intent(context, com.enhanced.videowidget.ui.VideoWidgetManagerActivity::class.java).apply {
                putExtra(com.enhanced.videowidget.ui.VideoWidgetManagerActivity.EXTRA_WIDGET_ID, appWidgetId)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            val settingsPendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId * 10 + 2,
                settingsIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.settingsButton, settingsPendingIntent)
            
            // Widget container click (for general interaction)
            val widgetIntent = Intent(context, EnhancedVideoWidget::class.java).apply {
                action = ACTION_WIDGET_TAP
                putExtra(EXTRA_WIDGET_ID, appWidgetId)
            }
            val widgetPendingIntent = PendingIntent.getBroadcast(
                context, 
                appWidgetId * 10 + 3, 
                widgetIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, widgetPendingIntent)
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
        
        val widgetId = intent.getIntExtra(EXTRA_WIDGET_ID, -1)
        if (widgetId == -1) return
        
        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                Log.d(TAG, "Play/Pause action for widget $widgetId")
                handlePlayPauseAction(context, widgetId)
            }
            ACTION_SETTINGS -> {
                Log.d(TAG, "Settings action for widget $widgetId")
                handleSettingsAction(context, widgetId)
            }
            ACTION_WIDGET_TAP -> {
                Log.d(TAG, "Widget tap action for widget $widgetId")
                handleWidgetTapAction(context, widgetId)
            }
        }
    }
    
    private fun handlePlayPauseAction(context: Context, widgetId: Int) {
        val repository = WidgetRepository.getInstance()

        // Use coroutine scope for async repository operations
        CoroutineScope(Dispatchers.Main).launch {
            val updatedWidget = repository.toggleWidgetPlayPause(widgetId)

            updatedWidget?.let { widget ->
                // Update widget UI immediately
                val appWidgetManager = AppWidgetManager.getInstance(context)
                updateAppWidget(context, appWidgetManager, widgetId, widget)

                // Manage WorkManager based on widget state
                if (widget.isPlaying && !widget.isPaused) {
                    // Widget is playing - start WorkManager
                    AppLogger.d(AppLogger.TAG_WIDGET, "Widget $widgetId resumed - starting WorkManager")
                    startWidgetUpdateWork(context, widgetId)
                } else if (widget.isPaused) {
                    // Widget is paused - stop WorkManager to save CPU
                    AppLogger.d(AppLogger.TAG_WIDGET, "Widget $widgetId paused - stopping WorkManager to save CPU")
                    stopWidgetUpdateWork(context, widgetId)
                } else {
                    // Widget is stopped - stop WorkManager
                    AppLogger.d(AppLogger.TAG_WIDGET, "Widget $widgetId stopped - stopping WorkManager")
                    stopWidgetUpdateWork(context, widgetId)
                }
            }
        }
    }
    
    private fun handleSettingsAction(context: Context, widgetId: Int) {
        // Open widget configuration activity
        // This would typically open a settings screen
        Log.d(TAG, "Settings action not implemented yet for widget $widgetId")
    }
    
    private fun handleWidgetTapAction(context: Context, widgetId: Int) {
        // Handle general widget tap - could show info or toggle play/pause
        handlePlayPauseAction(context, widgetId)
    }
    
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        AppLogger.i(AppLogger.TAG_WIDGET, "=== WIDGET UPDATE CALLED ===")
        AppLogger.d(AppLogger.TAG_WIDGET, "Updating widgets: ${appWidgetIds.contentToString()}")

        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
        AppLogger.d(AppLogger.TAG_WIDGET, "Repository initialized for widget updates")
        
        for (appWidgetId in appWidgetIds) {
            val widget = repository.getWidget(appWidgetId)
            
            if (widget != null) {
                AppLogger.d(AppLogger.TAG_WIDGET, "Widget $appWidgetId found - isPlaying: ${widget.isPlaying}, isPaused: ${widget.isPaused}, totalFrames: ${widget.totalFrames}, videoPath: ${widget.videoPath}")

                // Update widget UI
                updateAppWidget(context, appWidgetManager, appWidgetId, widget)

                // Start processing if widget should be playing (includes frame extraction)
                val shouldStart = widget.shouldStartProcessing()
                AppLogger.d(AppLogger.TAG_WIDGET, "Widget $appWidgetId shouldStartProcessing: $shouldStart")

                if (shouldStart) {
                    AppLogger.i(AppLogger.TAG_WIDGET, "Starting WorkManager for widget $appWidgetId")
                    startWidgetUpdateWork(context, appWidgetId)
                } else {
                    AppLogger.d(AppLogger.TAG_WIDGET, "Not starting WorkManager for widget $appWidgetId")
                }
            } else {
                // Widget not found in repository, show placeholder
                updateAppWidget(context, appWidgetManager, appWidgetId)
            }
        }
    }
    
    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        AppLogger.i(AppLogger.TAG_WIDGET, "=== WIDGETS DELETED: ${appWidgetIds.contentToString()} ===")
        
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
        
        for (appWidgetId in appWidgetIds) {
            // Stop any running work for this widget
            stopWidgetUpdateWork(context, appWidgetId)
            
            // Remove widget from repository
            repository.removeWidget(appWidgetId)
        }
    }
    
    override fun onEnabled(context: Context) {
        AppLogger.i(AppLogger.TAG_WIDGET, "=== WIDGET PROVIDER ENABLED ===")
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)

        // Request battery optimization exemption
        requestBatteryOptimizationExemption(context)
    }

    override fun onDisabled(context: Context) {
        AppLogger.i(AppLogger.TAG_WIDGET, "=== WIDGET PROVIDER DISABLED ===")
        // Stop all widget update work
        WorkManager.getInstance(context).cancelAllWorkByTag("widget_update")
        AppLogger.d(AppLogger.TAG_WIDGET, "All widget update work cancelled")
    }
    
    private fun startWidgetUpdateWork(context: Context, appWidgetId: Int) {
        AppLogger.d(AppLogger.TAG_WIDGET, "=== STARTING WORKMANAGER for Widget $appWidgetId ===")

        val workManager = WorkManager.getInstance(context)

        // Check if work is already running for this widget
        try {
            val workInfos = workManager.getWorkInfosForUniqueWork("widget_update_$appWidgetId").get()
            val hasRunningWork = workInfos.any { it.state == WorkInfo.State.RUNNING }

            if (hasRunningWork) {
                AppLogger.d(AppLogger.TAG_WIDGET, "WorkManager already running for widget $appWidgetId, skipping")
                return
            }

            AppLogger.d(AppLogger.TAG_WIDGET, "No running work found for widget $appWidgetId, starting new work")
        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_WIDGET, "Failed to check WorkManager status", e)
        }

        val inputData = Data.Builder()
            .putInt(EXTRA_WIDGET_ID, appWidgetId)
            .build()

        val workRequest = OneTimeWorkRequest.Builder(WidgetUpdateWorker::class.java)
            .setInputData(inputData)
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .addTag("widget_update")
            .addTag("widget_$appWidgetId")
            .build()

        AppLogger.d(AppLogger.TAG_WIDGET, "WorkRequest created for widget $appWidgetId")

        // Start foreground service to keep app alive
        val serviceIntent = Intent(context, WidgetUpdateService::class.java).apply {
            action = WidgetUpdateService.ACTION_START_UPDATES
        }
        context.startForegroundService(serviceIntent)
        AppLogger.d(AppLogger.TAG_WIDGET, "Foreground service started for widget $appWidgetId")

        workManager.enqueueUniqueWork(
            "widget_update_$appWidgetId",
            ExistingWorkPolicy.KEEP,  // Changed from REPLACE to KEEP
            workRequest
        )

        AppLogger.d(AppLogger.TAG_WIDGET, "WorkRequest enqueued for widget $appWidgetId")
    }
    
    private fun stopWidgetUpdateWork(context: Context, appWidgetId: Int) {
        AppLogger.d(AppLogger.TAG_WIDGET, "=== STOPPING WORKMANAGER for Widget $appWidgetId ===")
        val workManager = WorkManager.getInstance(context)

        // Cancel the specific work for this widget
        workManager.cancelUniqueWork("widget_update_$appWidgetId")

        // Also cancel by tag to ensure all related work is stopped
        workManager.cancelAllWorkByTag("widget_$appWidgetId")

        AppLogger.d(AppLogger.TAG_WIDGET, "WorkManager stopped for widget $appWidgetId")
    }

    private fun requestBatteryOptimizationExemption(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!powerManager.isIgnoringBatteryOptimizations(context.packageName)) {
                AppLogger.i(AppLogger.TAG_WIDGET, "Requesting battery optimization exemption for widget")
                try {
                    val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                        data = Uri.parse("package:${context.packageName}")
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    context.startActivity(intent)
                } catch (e: Exception) {
                    AppLogger.e(AppLogger.TAG_WIDGET, "Failed to request battery optimization exemption", e)
                }
            } else {
                AppLogger.d(AppLogger.TAG_WIDGET, "Battery optimization already disabled for widget")
            }
        }
    }
}
