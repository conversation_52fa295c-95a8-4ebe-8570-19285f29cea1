// Generated by view binder compiler. Do not edit!
package com.enhanced.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.enhanced.videowidget.R;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVideoWidgetManagerBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button buttonDelete;

  @NonNull
  public final Button buttonPlayPause;

  @NonNull
  public final Button buttonUpdate;

  @NonNull
  public final ImageView imagePreview;

  @NonNull
  public final SwitchMaterial switchShowPausedFrame;

  @NonNull
  public final TextView textWidgetDescription;

  @NonNull
  public final TextView textWidgetDetails;

  @NonNull
  public final TextView textWidgetName;

  private ActivityVideoWidgetManagerBinding(@NonNull ScrollView rootView,
      @NonNull Button buttonDelete, @NonNull Button buttonPlayPause, @NonNull Button buttonUpdate,
      @NonNull ImageView imagePreview, @NonNull SwitchMaterial switchShowPausedFrame,
      @NonNull TextView textWidgetDescription, @NonNull TextView textWidgetDetails,
      @NonNull TextView textWidgetName) {
    this.rootView = rootView;
    this.buttonDelete = buttonDelete;
    this.buttonPlayPause = buttonPlayPause;
    this.buttonUpdate = buttonUpdate;
    this.imagePreview = imagePreview;
    this.switchShowPausedFrame = switchShowPausedFrame;
    this.textWidgetDescription = textWidgetDescription;
    this.textWidgetDetails = textWidgetDetails;
    this.textWidgetName = textWidgetName;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVideoWidgetManagerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVideoWidgetManagerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_video_widget_manager, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVideoWidgetManagerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDelete;
      Button buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.buttonPlayPause;
      Button buttonPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayPause == null) {
        break missingId;
      }

      id = R.id.buttonUpdate;
      Button buttonUpdate = ViewBindings.findChildViewById(rootView, id);
      if (buttonUpdate == null) {
        break missingId;
      }

      id = R.id.imagePreview;
      ImageView imagePreview = ViewBindings.findChildViewById(rootView, id);
      if (imagePreview == null) {
        break missingId;
      }

      id = R.id.switchShowPausedFrame;
      SwitchMaterial switchShowPausedFrame = ViewBindings.findChildViewById(rootView, id);
      if (switchShowPausedFrame == null) {
        break missingId;
      }

      id = R.id.textWidgetDescription;
      TextView textWidgetDescription = ViewBindings.findChildViewById(rootView, id);
      if (textWidgetDescription == null) {
        break missingId;
      }

      id = R.id.textWidgetDetails;
      TextView textWidgetDetails = ViewBindings.findChildViewById(rootView, id);
      if (textWidgetDetails == null) {
        break missingId;
      }

      id = R.id.textWidgetName;
      TextView textWidgetName = ViewBindings.findChildViewById(rootView, id);
      if (textWidgetName == null) {
        break missingId;
      }

      return new ActivityVideoWidgetManagerBinding((ScrollView) rootView, buttonDelete,
          buttonPlayPause, buttonUpdate, imagePreview, switchShowPausedFrame, textWidgetDescription,
          textWidgetDetails, textWidgetName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
