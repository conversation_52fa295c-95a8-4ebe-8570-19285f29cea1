package com.enhanced.videowidget.ui

import android.Manifest
import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.provider.MediaStore
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.enhanced.videowidget.utils.AppLogger
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
// import com.enhanced.videowidget.databinding.ActivityWidgetConfigurationBinding
import com.enhanced.videowidget.utils.VideoFrameExtractor
import com.enhanced.videowidget.utils.VideoFileManager
import com.enhanced.videowidget.utils.PermissionManager
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import com.enhanced.videowidget.worker.WidgetUpdateWorker
import androidx.work.Data
import androidx.work.OneTimeWorkRequest
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import kotlinx.coroutines.launch

/**
 * Configuration activity for setting up new video widgets
 */
class WidgetConfigurationActivity : AppCompatActivity() {
    
    companion object {
        // Configuration constants can be added here if needed
    }
    
    // private lateinit var binding: ActivityWidgetConfigurationBinding
    private lateinit var repository: WidgetRepository
    private lateinit var videoFileManager: VideoFileManager
    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID
    private var selectedVideoUri: Uri? = null
    private var selectedVideoPath: String? = null
    
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        // Enhanced logging for video picker results
        val hasData = result.data?.data != null
        val dataUri = result.data?.data?.toString()

        AppLogger.logVideoPickerState(result.resultCode, hasData, dataUri)
        AppLogger.i(AppLogger.TAG_CONFIG, "Video picker result code: ${result.resultCode}")

        when (result.resultCode) {
            Activity.RESULT_OK -> {
                result.data?.data?.let { uri ->
                    AppLogger.i(AppLogger.TAG_CONFIG, "Video URI received: $uri")
                    AppLogger.logVideoSelection(null, uri.toString())
                    AppLogger.logcatOnly("videowidget-picker", "I", "SUCCESS: Video selected - $uri")
                    handleVideoSelection(uri)
                } ?: run {
                    AppLogger.e(AppLogger.TAG_CONFIG, "Video picker returned OK but no URI data")
                    AppLogger.logcatOnly("videowidget-picker", "E", "ERROR: RESULT_OK but no data")
                    showError("No video was selected")
                }
            }
            Activity.RESULT_CANCELED -> {
                AppLogger.w(AppLogger.TAG_CONFIG, "Video picker was cancelled by user")
                AppLogger.logcatOnly("videowidget-picker", "W", "CANCELLED: User cancelled video selection")
                showError("Video selection was cancelled")
            }
            else -> {
                AppLogger.e(AppLogger.TAG_CONFIG, "Video picker failed with result code: ${result.resultCode}")
                AppLogger.logcatOnly("videowidget-picker", "E", "FAILED: Unknown result code ${result.resultCode}")
                showError("Failed to select video. Please try again.")
            }
        }
    }

    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            openVideoPicker()
        } else {
            // Check if user denied permanently
            val deniedPermissions = permissions.filterValues { !it }.keys
            val shouldShowRationale = deniedPermissions.any { permission ->
                ActivityCompat.shouldShowRequestPermissionRationale(this, permission)
            }

            if (shouldShowRationale) {
                showError(getString(R.string.permission_video_required))
            } else {
                showError(getString(R.string.permission_settings_required))
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_widget_configuration)

        // Set context for toast debugging
        AppLogger.setContext(this)

        // Log app state for debugging
        AppLogger.logAppState(this)

        AppLogger.i(AppLogger.TAG_CONFIG, "=== WIDGET CONFIGURATION ACTIVITY STARTED ===")
        AppLogger.d(AppLogger.TAG_CONFIG, "Debug logging enabled: ${AppLogger.isDebugEnabled()}")
        AppLogger.d(AppLogger.TAG_CONFIG, "Verbose logging enabled: ${AppLogger.isVerboseEnabled()}")
        AppLogger.logcatOnly(AppLogger.TAG_CONFIG, "I", "Activity onCreate completed")

        // Log system info for debugging
        AppLogger.logcatOnly("videowidget-system", "I", "Android version: ${android.os.Build.VERSION.SDK_INT}")
        AppLogger.logcatOnly("videowidget-system", "I", "Device: ${android.os.Build.DEVICE}")
        AppLogger.logcatOnly("videowidget-system", "I", "Model: ${android.os.Build.MODEL}")

        // Log intent info
        AppLogger.logcatOnly("videowidget-intent", "I", "Widget ID: $appWidgetId")
        AppLogger.logcatOnly("videowidget-intent", "I", "Intent action: ${intent.action}")
        AppLogger.logcatOnly("videowidget-intent", "I", "Intent extras: ${intent.extras?.keySet()?.joinToString()}")
        
        // Set the result to CANCELED initially
        setResult(RESULT_CANCELED)
        
        // Get the widget ID from the intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID
        
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }
        
        setupRepository()
        setupUI()
        requestBatteryOptimizationExemption()
    }
    
    private fun setupRepository() {
        repository = WidgetRepository.getInstance()
        repository.initialize(this)
        videoFileManager = VideoFileManager(this)
    }
    
    private fun setupUI() {
        // Set up toolbar
        val toolbar = findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.apply {
            title = getString(R.string.widget_configuration_title)
            setDisplayHomeAsUpEnabled(true)
        }

        // Set up video selection button
        val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)
        buttonSelectVideo.setOnClickListener {
            checkPermissionsAndOpenPicker()
        }

        // Add long click for testing with a sample video (if available)
        buttonSelectVideo.setOnLongClickListener {
            AppLogger.i(AppLogger.TAG_CONFIG, "Long click detected - checking for test video")
            testWithSampleVideo()
            true
        }

        // Set up create widget button with double tap for debug mode
        val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
        var lastClickTime = 0L
        buttonCreateWidget.setOnClickListener {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastClickTime < 500) {
                // Double tap detected
                AppLogger.i(AppLogger.TAG_CONFIG, "Double tap detected - creating debug widget")
                createDebugWidget()
            } else {
                // Single tap
                createWidget()
            }
            lastClickTime = currentTime
        }



        // Set up cancel button
        val buttonCancel = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCancel)
        buttonCancel.setOnClickListener {
            finish()
        }

        // Initially disable create button
        buttonCreateWidget.isEnabled = false
    }

    private fun checkPermissionsAndOpenPicker() {
        AppLogger.i(AppLogger.TAG_CONFIG, "Checking permissions and opening picker...")

        if (PermissionManager.hasStoragePermissions(this)) {
            AppLogger.d(AppLogger.TAG_CONFIG, "Storage permissions granted, opening video picker")
            openVideoPicker()
        } else {
            AppLogger.w(AppLogger.TAG_CONFIG, "Storage permissions not granted")
            // Show permission explanation if needed
            if (PermissionManager.shouldShowPermissionRationale(this)) {
                AppLogger.d(AppLogger.TAG_CONFIG, "Showing permission rationale")
                showPermissionExplanation()
            } else {
                AppLogger.d(AppLogger.TAG_CONFIG, "Requesting storage permissions")
                PermissionManager.requestStoragePermissions(this)
            }
        }
    }

    private fun showPermissionExplanation() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Storage Permission Required")
            .setMessage(PermissionManager.getPermissionExplanation())
            .setPositiveButton("Grant Permission") { _, _ ->
                PermissionManager.requestStoragePermissions(this)
            }
            .setNegativeButton("Cancel") { _, _ ->
                finish()
            }
            .show()
    }

    private fun requestBatteryOptimizationExemption() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = getSystemService(POWER_SERVICE) as PowerManager
            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                AppLogger.i(AppLogger.TAG_CONFIG, "Requesting battery optimization exemption")
                try {
                    val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                        data = Uri.parse("package:$packageName")
                    }
                    startActivity(intent)
                } catch (e: Exception) {
                    AppLogger.e(AppLogger.TAG_CONFIG, "Failed to request battery optimization exemption", e)
                }
            } else {
                AppLogger.d(AppLogger.TAG_CONFIG, "Battery optimization already disabled")
            }
        }
    }

    private fun openVideoPicker() {
        try {
            AppLogger.i(AppLogger.TAG_CONFIG, "Opening video picker...")

            // Try multiple approaches to ensure video picker works
            val intents = listOf(
                // Modern media picker (Android 13+)
                Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI).apply {
                    type = "video/*"
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                },
                // Generic content picker
                Intent(Intent.ACTION_GET_CONTENT).apply {
                    type = "video/*"
                    addCategory(Intent.CATEGORY_OPENABLE)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                },
                // Document picker fallback
                Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                    type = "video/*"
                    addCategory(Intent.CATEGORY_OPENABLE)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
            )

            var intentLaunched = false
            for ((index, intent) in intents.withIndex()) {
                val resolveInfo = intent.resolveActivity(packageManager)
                AppLogger.logcatOnly("videowidget-picker", "D", "Testing intent ${index + 1}: ${intent.action}, type: ${intent.type}")
                AppLogger.logcatOnly("videowidget-picker", "D", "Resolve result: $resolveInfo")

                if (resolveInfo != null) {
                    AppLogger.d(AppLogger.TAG_CONFIG, "Launching video picker with intent ${index + 1}")
                    AppLogger.logcatOnly("videowidget-picker", "I", "LAUNCHING: Intent ${index + 1} - ${intent.action}")
                    videoPickerLauncher.launch(intent)
                    intentLaunched = true
                    break
                }
            }

            if (!intentLaunched) {
                AppLogger.e(AppLogger.TAG_CONFIG, "No app found to handle video picking")
                AppLogger.logcatOnly("videowidget-picker", "E", "FATAL: No app can handle video picking intents")
                showError("No video picker app found. Please install a file manager or gallery app.")
            }

        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_CONFIG, "Error opening video picker", e)
            showError("Error opening video picker: ${e.message}")
        }
    }
    
    private fun handleVideoSelection(videoUri: Uri) {
        AppLogger.i(AppLogger.TAG_CONFIG, "=== HANDLING VIDEO SELECTION ===")
        AppLogger.d(AppLogger.TAG_CONFIG, "Selected video URI: $videoUri")

        selectedVideoUri = videoUri

        // Show loading while copying video
        val textViewSelectedVideo = findViewById<android.widget.TextView>(R.id.textViewSelectedVideo)
        val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
        val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)

        updateProgress(true, "Copying video to secure storage...")
        textViewSelectedVideo.text = "Copying video..."
        buttonSelectVideo.isEnabled = false
        buttonCreateWidget.isEnabled = false

        lifecycleScope.launch {
            try {
                // Copy video to private directory
                AppLogger.d(AppLogger.TAG_CONFIG, "Starting video copy to private directory")
                val copiedVideoPath = videoFileManager.copyVideoToPrivateDirectory(videoUri)

                if (copiedVideoPath != null) {
                    selectedVideoPath = copiedVideoPath
                    AppLogger.i(AppLogger.TAG_CONFIG, "Video successfully copied to: $copiedVideoPath")

                    updateProgress(true, "Analyzing video properties...")

                    // Show video preview using the copied file
                    try {
                        val frameExtractor = VideoFrameExtractor()
                        AppLogger.d(AppLogger.TAG_CONFIG, "Getting video metadata...")
                        val metadata = frameExtractor.getVideoMetadata(copiedVideoPath)

                        if (metadata != null) {
                            AppLogger.i(AppLogger.TAG_CONFIG, "Video metadata: ${metadata.duration}ms, ${metadata.width}x${metadata.height}, ${metadata.frameRate}fps")

                            updateProgress(true, "Extracting preview frame...")
                            val previewFrame = frameExtractor.extractFrameAtTime(copiedVideoPath, 0)

                            if (previewFrame != null) {
                                AppLogger.d(AppLogger.TAG_CONFIG, "Preview frame extracted successfully")
                                val imageViewPreview = findViewById<android.widget.ImageView>(R.id.imageViewPreview)
                                imageViewPreview.setImageBitmap(previewFrame)
                                imageViewPreview.visibility = android.view.View.VISIBLE
                            } else {
                                AppLogger.w(AppLogger.TAG_CONFIG, "Failed to extract preview frame")
                            }

                            val textViewVideoInfo = findViewById<android.widget.TextView>(R.id.textViewVideoInfo)
                            textViewVideoInfo.text = "Duration: ${metadata.duration / 1000}s, ${metadata.width}x${metadata.height}"
                            textViewVideoInfo.visibility = android.view.View.VISIBLE

                            textViewSelectedVideo.text = getString(R.string.video_selected)
                            buttonCreateWidget.isEnabled = true
                        } else {
                            AppLogger.e(AppLogger.TAG_CONFIG, "Failed to get video metadata - video may be corrupted or unsupported")
                            showError("Video format not supported or file is corrupted")
                            textViewSelectedVideo.text = getString(R.string.select_video_prompt)
                        }
                    } catch (e: Exception) {
                        AppLogger.e(AppLogger.TAG_CONFIG, "Error analyzing video", e)
                        showError("Error analyzing video: ${e.message}")
                        textViewSelectedVideo.text = getString(R.string.select_video_prompt)
                    }
                } else {
                    AppLogger.e(AppLogger.TAG_CONFIG, "Failed to copy video to private directory")
                    showError("Failed to copy video. Please try again.")
                    textViewSelectedVideo.text = getString(R.string.select_video_prompt)
                }
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_CONFIG, "Error handling video selection", e)
                showError("Error processing video. Please try again.")
                textViewSelectedVideo.text = getString(R.string.select_video_prompt)
            } finally {
                updateProgress(false)
                buttonSelectVideo.isEnabled = true
            }
        }
    }
    
    private fun getVideoPath(uri: Uri): String? {
        val projection = arrayOf(MediaStore.Video.Media.DATA)
        contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
                return cursor.getString(columnIndex)
            }
        }
        return null
    }
    
    private fun createWidget() {
        val videoPath = selectedVideoPath
        AppLogger.i(AppLogger.TAG_CONFIG, "createWidget called - videoPath: $videoPath")

        if (videoPath == null) {
            AppLogger.e(AppLogger.TAG_CONFIG, "Cannot create widget - video path is null")
            showError("No video selected. Please select a video first.")
            return
        }

        AppLogger.d(AppLogger.TAG_CONFIG, "Video path validated: $videoPath")
        
        lifecycleScope.launch {
            try {
                // Show loading
                val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
                val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)
                updateProgress(true, "Creating widget...")
                buttonCreateWidget.isEnabled = false
                buttonSelectVideo.isEnabled = false

                AppLogger.i(AppLogger.TAG_CONFIG, "Starting widget creation process")

                // Extract video metadata
                updateProgress(true, "Verifying video file...")
                val frameExtractor = VideoFrameExtractor()
                val metadata = frameExtractor.getVideoMetadata(videoPath)

                if (metadata == null) {
                    AppLogger.e(AppLogger.TAG_CONFIG, "Failed to get video metadata during widget creation")
                    showError("Video file is corrupted or unsupported format")
                    return@launch
                }

                AppLogger.d(AppLogger.TAG_CONFIG, "Video metadata verified for widget creation")
                
                // Get widget name from input or use default
                val editTextWidgetName = findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.editTextWidgetName)
                val widgetName = editTextWidgetName.text.toString().ifEmpty {
                    "Video Widget $appWidgetId"
                }
                
                updateProgress(true, "Saving widget configuration...")

                // Create widget
                val widget = Widget(
                    id = appWidgetId,
                    name = widgetName,
                    description = "Duration: ${metadata.duration / 1000}s",
                    videoPath = videoPath,
                    framerate = metadata.frameRate,
                    mediaType = "video"
                )

                AppLogger.d(AppLogger.TAG_CONFIG, "Widget object created: $widget")

                // Add widget to repository
                repository.addOrUpdateWidget(widget)
                AppLogger.d(AppLogger.TAG_CONFIG, "Widget saved to repository")

                updateProgress(true, "Initializing widget display...")

                // Update the widget
                val appWidgetManager = AppWidgetManager.getInstance(this@WidgetConfigurationActivity)
                EnhancedVideoWidget.updateAppWidget(
                    this@WidgetConfigurationActivity,
                    appWidgetManager,
                    appWidgetId,
                    widget
                )

                AppLogger.i(AppLogger.TAG_CONFIG, "Widget display updated successfully")

                // Manually trigger onUpdate to ensure WorkManager starts
                AppLogger.d(AppLogger.TAG_CONFIG, "Manually triggering widget update to start WorkManager")
                EnhancedVideoWidget().onUpdate(
                    this@WidgetConfigurationActivity,
                    appWidgetManager,
                    intArrayOf(appWidgetId)
                )
                
                // Set result and finish
                val resultValue = Intent().apply {
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                }
                setResult(RESULT_OK, resultValue)
                
                Toast.makeText(this@WidgetConfigurationActivity, getString(R.string.widget_created), Toast.LENGTH_SHORT).show()
                finish()
                
            } catch (e: Exception) {
                AppLogger.logError(AppLogger.TAG_CONFIG, "creating widget", e)
                showError(getString(R.string.error_widget_creation))
            } finally {
                // Hide loading
                updateProgress(false)
                val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
                val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)
                buttonCreateWidget.isEnabled = true
                buttonSelectVideo.isEnabled = true
            }
        }
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    private fun updateProgress(isVisible: Boolean, message: String = "Processing...") {
        val progressContainer = findViewById<android.widget.LinearLayout>(R.id.progressContainer)
        val progressText = findViewById<android.widget.TextView>(R.id.progressText)

        progressContainer.visibility = if (isVisible) android.view.View.VISIBLE else android.view.View.GONE
        progressText.text = message

        if (isVisible) {
            AppLogger.i(AppLogger.TAG_CONFIG, "Progress: $message")
        }
    }

    private fun testWithSampleVideo() {
        AppLogger.i(AppLogger.TAG_CONFIG, "Testing with sample video...")

        // Try various locations where the sample video might be
        val sampleVideoPaths = listOf(
            "/data/local/tmp/test_video.mp4",
            "/sdcard/Download/test_video.mp4",
            "/storage/emulated/0/Download/test_video.mp4",
            "/sdcard/test_video.mp4",
            "/storage/emulated/0/test_video.mp4"
        )

        var foundVideo: String? = null

        // Check for sample video in various locations
        for (path in sampleVideoPaths) {
            val file = java.io.File(path)
            if (file.exists() && file.canRead() && file.length() > 0) {
                foundVideo = path
                AppLogger.i(AppLogger.TAG_CONFIG, "Found sample video: $foundVideo")
                break
            }
        }

        if (foundVideo == null) {
            AppLogger.d(AppLogger.TAG_CONFIG, "Sample video not found in expected locations")

            // Check common video locations on Android
            val testPaths = listOf(
                "/sdcard/DCIM/Camera/",
                "/sdcard/Movies/",
                "/sdcard/Download/",
                "/storage/emulated/0/DCIM/Camera/",
                "/storage/emulated/0/Movies/",
                "/storage/emulated/0/Download/"
            )

            for (path in testPaths) {
                try {
                    val dir = java.io.File(path)
                    if (dir.exists() && dir.isDirectory) {
                        val videoFiles = dir.listFiles { file ->
                            file.isFile && (file.name.endsWith(".mp4", true) ||
                                           file.name.endsWith(".mov", true) ||
                                           file.name.endsWith(".3gp", true))
                        }

                        if (!videoFiles.isNullOrEmpty()) {
                            foundVideo = videoFiles.first().absolutePath
                            AppLogger.i(AppLogger.TAG_CONFIG, "Found test video: $foundVideo")
                            break
                        }
                    }
                } catch (e: Exception) {
                    AppLogger.d(AppLogger.TAG_CONFIG, "Could not access directory: $path")
                }
            }
        }

        if (foundVideo != null) {
            // Simulate video selection with found video
            selectedVideoPath = foundVideo
            AppLogger.i(AppLogger.TAG_CONFIG, "Using test video: $foundVideo")

            val textViewSelectedVideo = findViewById<android.widget.TextView>(R.id.textViewSelectedVideo)
            val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
            textViewSelectedVideo.text = "Test video selected: ${java.io.File(foundVideo).name}"
            buttonCreateWidget.isEnabled = true

            showError("Test video selected: ${java.io.File(foundVideo).name}")
        } else {
            AppLogger.w(AppLogger.TAG_CONFIG, "No test video found in common directories")
            showError("No test video found. Please push the sample video to the device first.")
        }
    }

    private fun createDebugWidget() {
        AppLogger.i(AppLogger.TAG_CONFIG, "Creating debug widget without video...")

        lifecycleScope.launch {
            try {
                updateProgress(true, "Creating debug widget...")

                // Create a debug widget without video
                val widget = Widget(
                    id = appWidgetId,
                    name = "Debug Widget",
                    description = "Debug widget for testing",
                    videoPath = null, // No video path
                    framerate = 30f,
                    mediaType = "debug"
                )

                AppLogger.d(AppLogger.TAG_CONFIG, "Debug widget object created: $widget")

                // Add widget to repository
                repository.addOrUpdateWidget(widget)
                AppLogger.d(AppLogger.TAG_CONFIG, "Debug widget saved to repository")

                // Update the widget display
                val appWidgetManager = AppWidgetManager.getInstance(this@WidgetConfigurationActivity)
                EnhancedVideoWidget.updateAppWidget(
                    this@WidgetConfigurationActivity,
                    appWidgetManager,
                    appWidgetId,
                    widget
                )

                AppLogger.i(AppLogger.TAG_CONFIG, "Debug widget created successfully")

                // Set result and finish
                val resultValue = Intent().apply {
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                }
                setResult(Activity.RESULT_OK, resultValue)
                finish()

            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_CONFIG, "Error creating debug widget", e)
                showError("Error creating debug widget: ${e.message}")
            } finally {
                updateProgress(false)
            }
        }
    }



    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        PermissionManager.handlePermissionResult(
            requestCode,
            permissions,
            grantResults,
            onPermissionGranted = { openVideoPicker() },
            onPermissionDenied = {
                showError("Storage permission is required to select videos")
                finish()
            }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        PermissionManager.handleActivityResult(
            requestCode,
            resultCode,
            this,
            onPermissionGranted = { openVideoPicker() },
            onPermissionDenied = {
                showError("Storage permission is required to select videos")
                finish()
            }
        )
    }
}
