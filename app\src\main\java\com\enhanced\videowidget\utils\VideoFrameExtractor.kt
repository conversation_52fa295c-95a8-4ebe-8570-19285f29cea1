package com.enhanced.videowidget.utils

import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * Utility class for extracting frames from video files
 */
class VideoFrameExtractor {
    
    companion object {
        private const val DEFAULT_MAX_FRAMES = 100
        private const val DEFAULT_TARGET_SIZE = 300
    }
    
    data class FrameInfo(
        val frameIndex: Int,
        val timestamp: Long,
        val filePath: String
    )
    
    /**
     * Extract frames from a video file
     */
    suspend fun extractFrames(
        videoPath: String,
        outputDirectory: String,
        maxFrames: Int = DEFAULT_MAX_FRAMES,
        targetWidth: Int = DEFAULT_TARGET_SIZE,
        targetHeight: Int = DEFAULT_TARGET_SIZE
    ): List<FrameInfo> = withContext(Dispatchers.IO) {

        AppLogger.i(AppLogger.TAG_FRAMES, "=== STARTING FRAME EXTRACTION ===")
        AppLogger.d(AppLogger.TAG_FRAMES, "Video path: $videoPath")
        AppLogger.d(AppLogger.TAG_FRAMES, "Output directory: $outputDirectory")
        AppLogger.d(AppLogger.TAG_FRAMES, "Max frames: $maxFrames, Target size: ${targetWidth}x${targetHeight}")

        val frameInfoList = mutableListOf<FrameInfo>()
        val retriever = MediaMetadataRetriever()

        try {
            AppLogger.d(AppLogger.TAG_FRAMES, "Setting data source...")
            retriever.setDataSource(videoPath)
            AppLogger.d(AppLogger.TAG_FRAMES, "Data source set successfully")

            // Get video duration and frame rate
            val durationString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val duration = durationString?.toLongOrNull() ?: 0L

            AppLogger.d(AppLogger.TAG_FRAMES, "Raw duration string: '$durationString'")
            AppLogger.d(AppLogger.TAG_FRAMES, "Parsed duration: ${duration}ms")

            if (duration <= 0) {
                AppLogger.e(AppLogger.TAG_FRAMES, "Invalid video duration: $duration")
                return@withContext emptyList()
            }

            // Calculate frame interval
            val frameInterval = duration / maxFrames

            AppLogger.i(AppLogger.TAG_FRAMES, "Video duration: ${duration}ms (${duration/1000}s)")
            AppLogger.i(AppLogger.TAG_FRAMES, "Frame interval: ${frameInterval}ms")
            AppLogger.i(AppLogger.TAG_FRAMES, "Will extract $maxFrames frames")
            
            // Create output directory
            val outputDir = File(outputDirectory)
            AppLogger.d(AppLogger.TAG_FRAMES, "Output directory: ${outputDir.absolutePath}")
            if (!outputDir.exists()) {
                val created = outputDir.mkdirs()
                AppLogger.d(AppLogger.TAG_FRAMES, "Created output directory: $created")
            } else {
                AppLogger.d(AppLogger.TAG_FRAMES, "Output directory already exists")
            }

            // Extract frames
            AppLogger.i(AppLogger.TAG_FRAMES, "Starting frame extraction loop...")
            for (i in 0 until maxFrames) {
                val timestamp = i * frameInterval * 1000 // Convert to microseconds
                AppLogger.v(AppLogger.TAG_FRAMES, "Extracting frame $i at timestamp ${timestamp}μs (${timestamp/1000}ms)")

                try {
                    val bitmap = retriever.getFrameAtTime(timestamp, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)

                    if (bitmap != null) {
                        AppLogger.v(AppLogger.TAG_FRAMES, "Frame $i extracted successfully - Size: ${bitmap.width}x${bitmap.height}")

                        // Resize bitmap if needed
                        val resizedBitmap = resizeBitmap(bitmap, targetWidth, targetHeight)
                        AppLogger.v(AppLogger.TAG_FRAMES, "Frame $i resized to: ${resizedBitmap.width}x${resizedBitmap.height}")

                        // Save frame to file
                        val frameFile = File(outputDir, "frame_$i.png")
                        val saved = saveFrameToFile(resizedBitmap, frameFile.absolutePath)
                        AppLogger.v(AppLogger.TAG_FRAMES, "Frame $i saved to file: $saved - ${frameFile.absolutePath}")

                        if (saved) {
                            frameInfoList.add(
                                FrameInfo(
                                    frameIndex = i,
                                    timestamp = timestamp / 1000, // Convert back to milliseconds
                                    filePath = frameFile.absolutePath
                                )
                            )
                            AppLogger.logFrameExtraction(i, maxFrames, true)
                        } else {
                            AppLogger.w(AppLogger.TAG_FRAMES, "Failed to save frame $i to file")
                        }

                        // Clean up bitmaps
                        if (resizedBitmap != bitmap) {
                            bitmap.recycle()
                        }
                        resizedBitmap.recycle()

                    } else {
                        AppLogger.w(AppLogger.TAG_FRAMES, "Failed to extract frame $i at timestamp ${timestamp / 1000}ms - bitmap is null")
                        AppLogger.logFrameExtraction(i, maxFrames, false)
                    }
                } catch (e: Exception) {
                    AppLogger.e(AppLogger.TAG_FRAMES, "Error extracting frame $i at timestamp ${timestamp / 1000}ms", e)
                    AppLogger.logFrameExtraction(i, maxFrames, false)
                }
            }

            AppLogger.i(AppLogger.TAG_FRAMES, "Frame extraction completed - Successfully extracted ${frameInfoList.size}/$maxFrames frames")

        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Critical error during frame extraction from: $videoPath", e)
        } finally {
            try {
                retriever.release()
                AppLogger.d(AppLogger.TAG_FRAMES, "MediaMetadataRetriever released successfully")
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_FRAMES, "Error releasing MediaMetadataRetriever", e)
            }
        }
        
        frameInfoList
    }
    
    /**
     * Extract a single frame at a specific timestamp
     */
    suspend fun extractFrameAtTime(
        videoPath: String,
        timestampMs: Long,
        targetWidth: Int = DEFAULT_TARGET_SIZE,
        targetHeight: Int = DEFAULT_TARGET_SIZE
    ): Bitmap? = withContext(Dispatchers.IO) {

        AppLogger.d(AppLogger.TAG_FRAMES, "Extracting frame at ${timestampMs}ms from: $videoPath")

        // Validate file first
        val file = java.io.File(videoPath)
        if (!file.exists() || !file.canRead() || file.length() == 0L) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Invalid video file for frame extraction: $videoPath")
            return@withContext null
        }

        val retriever = MediaMetadataRetriever()

        try {
            AppLogger.d(AppLogger.TAG_FRAMES, "Setting data source for frame extraction...")
            retriever.setDataSource(videoPath)
            AppLogger.d(AppLogger.TAG_FRAMES, "Data source set, extracting frame...")

            val timestampUs = timestampMs * 1000 // Convert to microseconds
            val bitmap = retriever.getFrameAtTime(timestampUs, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)

            if (bitmap != null) {
                AppLogger.d(AppLogger.TAG_FRAMES, "Frame extracted successfully - Size: ${bitmap.width}x${bitmap.height}")
                val resizedBitmap = resizeBitmap(bitmap, targetWidth, targetHeight)
                AppLogger.d(AppLogger.TAG_FRAMES, "Frame resized to: ${resizedBitmap.width}x${resizedBitmap.height}")
                return@withContext resizedBitmap
            } else {
                AppLogger.w(AppLogger.TAG_FRAMES, "Failed to extract frame at ${timestampMs}ms - bitmap is null")
                return@withContext null
            }

        } catch (e: IllegalArgumentException) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Invalid video format for frame extraction at ${timestampMs}ms: $videoPath", e)
            return@withContext null
        } catch (e: RuntimeException) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Video codec error during frame extraction at ${timestampMs}ms: $videoPath", e)
            return@withContext null
        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Unexpected error extracting frame at ${timestampMs}ms: $videoPath", e)
            return@withContext null
        } finally {
            try {
                retriever.release()
                AppLogger.d(AppLogger.TAG_FRAMES, "MediaMetadataRetriever released for frame extraction")
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_FRAMES, "Error releasing MediaMetadataRetriever for single frame", e)
            }
        }
    }
    
    /**
     * Get video metadata
     */
    suspend fun getVideoMetadata(videoPath: String): VideoMetadata? = withContext(Dispatchers.IO) {
        AppLogger.d(AppLogger.TAG_VIDEO, "Getting video metadata for: $videoPath")

        // First validate the file exists and is readable
        val file = java.io.File(videoPath)
        if (!file.exists()) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Video file does not exist: $videoPath")
            return@withContext null
        }

        if (!file.canRead()) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Video file is not readable: $videoPath")
            return@withContext null
        }

        if (file.length() == 0L) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Video file is empty: $videoPath")
            return@withContext null
        }

        AppLogger.d(AppLogger.TAG_VIDEO, "File validation passed - Size: ${file.length()} bytes")

        val retriever = MediaMetadataRetriever()

        try {
            AppLogger.d(AppLogger.TAG_VIDEO, "Setting data source...")
            retriever.setDataSource(videoPath)
            AppLogger.d(AppLogger.TAG_VIDEO, "Data source set successfully")

            // Extract all metadata with detailed error checking
            val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val widthStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
            val heightStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)
            val frameRateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)
            val mimeType = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE)
            val hasVideo = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_HAS_VIDEO)

            AppLogger.d(AppLogger.TAG_VIDEO, "Raw metadata - Duration: '$durationStr', Width: '$widthStr', Height: '$heightStr', FrameRate: '$frameRateStr', MimeType: '$mimeType', HasVideo: '$hasVideo'")

            // Check if this is actually a video file
            if (hasVideo != "yes") {
                AppLogger.e(AppLogger.TAG_VIDEO, "File does not contain video track: $videoPath")
                return@withContext null
            }

            val duration = durationStr?.toLongOrNull() ?: 0L
            val width = widthStr?.toIntOrNull() ?: 0
            val height = heightStr?.toIntOrNull() ?: 0
            val frameRate = frameRateStr?.toFloatOrNull() ?: 30f

            // Validate metadata values
            if (duration <= 0) {
                AppLogger.e(AppLogger.TAG_VIDEO, "Invalid video duration: $duration")
                return@withContext null
            }

            if (width <= 0 || height <= 0) {
                AppLogger.e(AppLogger.TAG_VIDEO, "Invalid video dimensions: ${width}x${height}")
                return@withContext null
            }

            AppLogger.logVideoMetadata(duration, frameRate, width, height)
            AppLogger.i(AppLogger.TAG_VIDEO, "Video metadata extraction successful - MimeType: $mimeType")

            return@withContext VideoMetadata(
                duration = duration,
                width = width,
                height = height,
                frameRate = frameRate
            )

        } catch (e: IllegalArgumentException) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Invalid video file format or corrupted file: $videoPath", e)
            return@withContext null
        } catch (e: RuntimeException) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Video codec not supported or file corrupted: $videoPath", e)
            return@withContext null
        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Unexpected error extracting video metadata: $videoPath", e)
            return@withContext null
        } finally {
            try {
                retriever.release()
                AppLogger.d(AppLogger.TAG_VIDEO, "MediaMetadataRetriever released")
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_VIDEO, "Error releasing MediaMetadataRetriever for metadata", e)
            }
        }
    }
    
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return if (bitmap.width != targetWidth || bitmap.height != targetHeight) {
            Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        } else {
            bitmap
        }
    }
    
    private fun saveFrameToFile(bitmap: Bitmap, filePath: String): Boolean {
        return try {
            AppLogger.v(AppLogger.TAG_FRAMES, "Saving frame to: $filePath")
            FileOutputStream(filePath).use { out ->
                val compressed = bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)
                AppLogger.v(AppLogger.TAG_FRAMES, "Frame compression result: $compressed")
                compressed
            }
        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Failed to save frame to $filePath", e)
            false
        }
    }
    
    data class VideoMetadata(
        val duration: Long,
        val width: Int,
        val height: Int,
        val frameRate: Float
    )
}
