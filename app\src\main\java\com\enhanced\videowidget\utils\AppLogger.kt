package com.enhanced.videowidget.utils

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import java.lang.ref.WeakReference

/**
 * Centralized logging utility with configurable debug levels
 */
object AppLogger {

    // Debug configuration - set to true to enable detailed logging
    // Change these values to control logging levels
    private const val DEBUG_ENABLED = true
    private const val VERBOSE_ENABLED = true
    private const val TOAST_DEBUG_ENABLED = false // Show debug logs as toast

    // Context for showing toast messages
    private var contextRef: WeakReference<Context>? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    // Quick method to check if debug logging is enabled
    fun isDebugEnabled() = DEBUG_ENABLED
    fun isVerboseEnabled() = VERBOSE_ENABLED

    // Set context for toast messages
    fun setContext(context: Context) {
        contextRef = WeakReference(context)
    }
    
    // Log tags for different components - all prefixed with "videowidget" for easy filtering
    const val TAG_WIDGET = "videowidget-widget"
    const val TAG_VIDEO = "videowidget-video"
    const val TAG_FRAMES = "videowidget-frames"
    const val TAG_WORKER = "videowidget-worker"
    const val TAG_CONFIG = "videowidget-config"
    const val TAG_REPOSITORY = "videowidget-repo"
    const val TAG_SERVICE = "videowidget-service"
    
    private fun showToast(message: String, isError: Boolean = false) {
        if (TOAST_DEBUG_ENABLED && DEBUG_ENABLED) {
            contextRef?.get()?.let { context ->
                mainHandler.post {
                    val duration = if (isError) Toast.LENGTH_LONG else Toast.LENGTH_SHORT
                    Toast.makeText(context, message, duration).show()
                }
            }
        }
    }

    fun d(tag: String, message: String) {
        // Always log to logcat
        Log.d(tag, message)
        if (DEBUG_ENABLED) {
            showToast("[$tag] $message")
        }
    }

    fun v(tag: String, message: String) {
        // Always log to logcat
        Log.v(tag, message)
        if (VERBOSE_ENABLED) {
            // Don't show verbose logs as toast to avoid spam
        }
    }

    fun i(tag: String, message: String) {
        // Always log to logcat
        Log.i(tag, message)
        showToast("ℹ️ [$tag] $message")
    }

    fun w(tag: String, message: String) {
        // Always log to logcat
        Log.w(tag, message)
        showToast("⚠️ [$tag] $message")
    }

    fun w(tag: String, message: String, throwable: Throwable) {
        // Always log to logcat
        Log.w(tag, message, throwable)
        showToast("⚠️ [$tag] $message: ${throwable.message}")
    }

    fun e(tag: String, message: String) {
        // Always log to logcat
        Log.e(tag, message)
        showToast("❌ [$tag] $message", true)
    }

    fun e(tag: String, message: String, throwable: Throwable) {
        // Always log to logcat
        Log.e(tag, message, throwable)
        showToast("❌ [$tag] $message: ${throwable.message}", true)
    }
    
    // Specialized logging methods for different components

    fun logVideoSelection(videoPath: String?, videoUri: String?) {
        Log.i(TAG_CONFIG, "=== VIDEO SELECTION ===")
        Log.i(TAG_CONFIG, "Video Path: $videoPath")
        Log.i(TAG_CONFIG, "Video URI: $videoUri")
        d(TAG_CONFIG, "=== VIDEO SELECTION ===")
        d(TAG_CONFIG, "Video Path: $videoPath")
        d(TAG_CONFIG, "Video URI: $videoUri")
    }

    fun logVideoMetadata(duration: Long, frameRate: Float, width: Int, height: Int) {
        Log.i(TAG_VIDEO, "=== VIDEO METADATA ===")
        Log.i(TAG_VIDEO, "Duration: ${duration}ms (${duration/1000}s)")
        Log.i(TAG_VIDEO, "Frame Rate: $frameRate fps")
        Log.i(TAG_VIDEO, "Resolution: ${width}x${height}")
        d(TAG_VIDEO, "=== VIDEO METADATA ===")
        d(TAG_VIDEO, "Duration: ${duration}ms (${duration/1000}s)")
        d(TAG_VIDEO, "Frame Rate: $frameRate fps")
        d(TAG_VIDEO, "Resolution: ${width}x${height}")
    }

    fun logFrameExtraction(frameIndex: Int, totalFrames: Int, success: Boolean) {
        Log.v(TAG_FRAMES, "Frame $frameIndex/$totalFrames - Success: $success")
        v(TAG_FRAMES, "Frame $frameIndex/$totalFrames - Success: $success")
    }

    fun logWidgetUpdate(widgetId: Int, frameIndex: Int, isPlaying: Boolean) {
        Log.v(TAG_WIDGET, "Widget $widgetId - Frame: $frameIndex, Playing: $isPlaying")
        v(TAG_WIDGET, "Widget $widgetId - Frame: $frameIndex, Playing: $isPlaying")
    }

    fun logWorkerStart(widgetId: Int) {
        Log.i(TAG_WORKER, "=== WORKER STARTED for Widget $widgetId ===")
        i(TAG_WORKER, "=== WORKER STARTED for Widget $widgetId ===")
    }

    fun logWorkerEnd(widgetId: Int, success: Boolean) {
        Log.i(TAG_WORKER, "=== WORKER ENDED for Widget $widgetId - Success: $success ===")
        i(TAG_WORKER, "=== WORKER ENDED for Widget $widgetId - Success: $success ===")
    }

    fun logError(tag: String, operation: String, error: Throwable) {
        Log.e(tag, "ERROR in $operation: ${error.message}", error)
        e(tag, "ERROR in $operation: ${error.message}", error)
    }

    fun logFileOperation(operation: String, path: String, success: Boolean) {
        Log.d(TAG_REPOSITORY, "File $operation: $path - Success: $success")
        d(TAG_REPOSITORY, "File $operation: $path - Success: $success")
    }

    // Additional logcat-specific methods for easier filtering
    fun logcatOnly(tag: String, level: String, message: String) {
        when (level.uppercase()) {
            "D" -> Log.d(tag, message)
            "I" -> Log.i(tag, message)
            "W" -> Log.w(tag, message)
            "E" -> Log.e(tag, message)
            "V" -> Log.v(tag, message)
            else -> Log.d(tag, message)
        }
    }

    fun logAppState(context: android.content.Context) {
        Log.i("videowidget-appstate", "=== APP STATE DEBUG ===")
        Log.i("videowidget-appstate", "Package: ${context.packageName}")
        Log.i("videowidget-appstate", "Debug enabled: $DEBUG_ENABLED")
        Log.i("videowidget-appstate", "Toast debug enabled: $TOAST_DEBUG_ENABLED")
        Log.i("videowidget-appstate", "Context set: ${contextRef?.get() != null}")
    }

    fun logVideoPickerState(resultCode: Int, hasData: Boolean, dataUri: String?) {
        Log.i("videowidget-picker", "=== VIDEO PICKER RESULT ===")
        Log.i("videowidget-picker", "Result code: $resultCode")
        Log.i("videowidget-picker", "Has data: $hasData")
        Log.i("videowidget-picker", "Data URI: $dataUri")
        Log.i("videowidget-picker", "Expected RESULT_OK: ${android.app.Activity.RESULT_OK}")
        Log.i("videowidget-picker", "Expected RESULT_CANCELED: ${android.app.Activity.RESULT_CANCELED}")
    }
}
