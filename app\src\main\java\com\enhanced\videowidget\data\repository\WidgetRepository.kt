package com.enhanced.videowidget.data.repository

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.utils.AppLogger
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * Enhanced Widget Repository with pause/play state management
 */
class WidgetRepository private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: WidgetRepository? = null
        private const val WIDGETS_FILE_NAME = "enhanced_widgets.json"
        private const val WIDGET_PREFS = "enhanced_widget_prefs"
        private const val FRAME_CACHE_DIR = "widget_frames"
        private const val PREF_SHOW_PAUSED_FRAME = "show_paused_frame"

        fun getInstance(): WidgetRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WidgetRepository().also { INSTANCE = it }
            }
        }
    }
    
    private var appContext: Context? = null
    private val _widgets = MutableLiveData<List<Widget>>()
    private val fileOperationLock = ReentrantLock()
    private val gson = Gson()
    private lateinit var sharedPrefs: SharedPreferences
    
    val widgets: LiveData<List<Widget>> = _widgets
    
    fun initialize(context: Context) {
        appContext = context.applicationContext
        sharedPrefs = context.getSharedPreferences(WIDGET_PREFS, Context.MODE_PRIVATE)
        loadWidgetsFromStorage()
        createFrameCacheDirectory()
    }
    
    private fun createFrameCacheDirectory() {
        appContext?.let { context ->
            val frameCacheDir = File(context.filesDir, FRAME_CACHE_DIR)
            if (!frameCacheDir.exists()) {
                frameCacheDir.mkdirs()
            }
        }
    }
    
    suspend fun addOrUpdateWidget(widget: Widget) = withContext(Dispatchers.IO) {
        fileOperationLock.withLock {
            val currentWidgets = _widgets.value?.toMutableList() ?: mutableListOf()
            val existingIndex = currentWidgets.indexOfFirst { it.id == widget.id }

            if (existingIndex >= 0) {
                currentWidgets[existingIndex] = widget
            } else {
                currentWidgets.add(widget)
            }

            _widgets.postValue(currentWidgets)
            saveWidgetsToStorage(currentWidgets)
        }
    }
    
    fun getWidget(widgetId: Int): Widget? {
        val widget = _widgets.value?.find { it.id == widgetId }
        AppLogger.d(AppLogger.TAG_REPOSITORY, "getWidget($widgetId) - Found: ${widget != null}, Total widgets: ${_widgets.value?.size ?: 0}")
        if (widget != null) {
            AppLogger.d(AppLogger.TAG_REPOSITORY, "Widget $widgetId details - isPlaying: ${widget.isPlaying}, isPaused: ${widget.isPaused}, totalFrames: ${widget.totalFrames}, videoPath: ${widget.videoPath}")
        }
        return widget
    }
    
    suspend fun updateWidgetPlaybackState(widgetId: Int, isPlaying: Boolean, isPaused: Boolean = false) {
        val widget = getWidget(widgetId)
        widget?.let {
            val updatedWidget = it.withPlaybackState(isPlaying, isPaused)
            addOrUpdateWidget(updatedWidget)
        }
    }

    suspend fun updateWidgetFrameIndex(widgetId: Int, frameIndex: Int) {
        val widget = getWidget(widgetId)
        widget?.let {
            val updatedWidget = it.withFrameIndex(frameIndex)
            addOrUpdateWidget(updatedWidget)
        }
    }
    
    suspend fun toggleWidgetPlayPause(widgetId: Int): Widget? {
        val widget = getWidget(widgetId)
        return widget?.let {
            val toggledWidget = it.togglePlayPause()
            addOrUpdateWidget(toggledWidget)
            toggledWidget
        }
    }
    
    fun removeWidget(widgetId: Int) {
        fileOperationLock.withLock {
            val currentWidgets = _widgets.value?.toMutableList() ?: mutableListOf()
            currentWidgets.removeAll { it.id == widgetId }
            _widgets.postValue(currentWidgets)
            saveWidgetsToStorage(currentWidgets)
            
            // Clean up frame files
            deleteWidgetFrames(widgetId)
        }
    }
    
    private fun deleteWidgetFrames(widgetId: Int) {
        appContext?.let { context ->
            val frameCacheDir = File(context.filesDir, FRAME_CACHE_DIR)
            val widgetFrameDir = File(frameCacheDir, "widget_$widgetId")
            if (widgetFrameDir.exists()) {
                widgetFrameDir.deleteRecursively()
            }
        }
    }
    
    suspend fun saveFrameBitmap(widgetId: Int, frameIndex: Int, bitmap: Bitmap): String? = withContext(Dispatchers.IO) {
        appContext?.let { context ->
            try {
                val frameCacheDir = File(context.filesDir, FRAME_CACHE_DIR)
                val widgetFrameDir = File(frameCacheDir, "widget_$widgetId")
                if (!widgetFrameDir.exists()) {
                    widgetFrameDir.mkdirs()
                }
                
                val frameFile = File(widgetFrameDir, "frame_$frameIndex.png")
                FileOutputStream(frameFile).use { out ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)
                }
                frameFile.absolutePath
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }
    
    suspend fun loadFrameBitmap(widgetId: Int, frameIndex: Int): Bitmap? = withContext(Dispatchers.IO) {
        appContext?.let { context ->
            try {
                val frameCacheDir = File(context.filesDir, FRAME_CACHE_DIR)
                val frameFile = File(frameCacheDir, "widget_$widgetId/frame_$frameIndex.png")
                if (frameFile.exists()) {
                    BitmapFactory.decodeFile(frameFile.absolutePath)
                } else null
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    /**
     * Synchronous version of loadFrameBitmap for use in widget updates
     * Should be used carefully as it performs file I/O on the calling thread
     */
    fun loadFrameBitmapSync(widgetId: Int, frameIndex: Int): Bitmap? {
        return appContext?.let { context ->
            try {
                val frameCacheDir = File(context.filesDir, FRAME_CACHE_DIR)
                val frameFile = File(frameCacheDir, "widget_$widgetId/frame_$frameIndex.png")
                if (frameFile.exists()) {
                    BitmapFactory.decodeFile(frameFile.absolutePath)
                } else null
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }
    
    private fun loadWidgetsFromStorage() {
        appContext?.let { context ->
            try {
                val file = File(context.filesDir, WIDGETS_FILE_NAME)
                AppLogger.d(AppLogger.TAG_REPOSITORY, "Loading widgets from storage - File exists: ${file.exists()}, Path: ${file.absolutePath}")
                if (file.exists()) {
                    val json = file.readText()
                    AppLogger.d(AppLogger.TAG_REPOSITORY, "Widget JSON content: $json")
                    val type = object : TypeToken<List<Widget>>() {}.type
                    val widgets: List<Widget> = gson.fromJson(json, type) ?: emptyList()
                    AppLogger.d(AppLogger.TAG_REPOSITORY, "Loaded ${widgets.size} widgets from storage")
                    _widgets.postValue(widgets)
                } else {
                    AppLogger.d(AppLogger.TAG_REPOSITORY, "Widget file does not exist, initializing empty list")
                    _widgets.postValue(emptyList())
                }
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_REPOSITORY, "Error loading widgets from storage", e)
                _widgets.postValue(emptyList())
            }
        }
    }
    
    private fun saveWidgetsToStorage(widgets: List<Widget>) {
        appContext?.let { context ->
            try {
                val file = File(context.filesDir, WIDGETS_FILE_NAME)
                val json = gson.toJson(widgets)
                file.writeText(json)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    fun getFrameDirectory(widgetId: Int): String? {
        return appContext?.let { context ->
            val frameCacheDir = File(context.filesDir, FRAME_CACHE_DIR)
            val widgetFrameDir = File(frameCacheDir, "widget_$widgetId")
            if (!widgetFrameDir.exists()) {
                widgetFrameDir.mkdirs()
            }
            widgetFrameDir.absolutePath
        }
    }

    /**
     * Get the global setting for showing paused frames
     */
    fun getShowPausedFrameSetting(): Boolean {
        return if (::sharedPrefs.isInitialized) {
            sharedPrefs.getBoolean(PREF_SHOW_PAUSED_FRAME, true) // Default to true
        } else {
            true
        }
    }

    /**
     * Set the global setting for showing paused frames
     */
    fun setShowPausedFrameSetting(showPausedFrame: Boolean) {
        if (::sharedPrefs.isInitialized) {
            sharedPrefs.edit()
                .putBoolean(PREF_SHOW_PAUSED_FRAME, showPausedFrame)
                .apply()
        }
    }

    /**
     * Update all widgets with the new pause frame setting
     */
    suspend fun updateAllWidgetsPauseFrameSetting(showPausedFrame: Boolean) = withContext(Dispatchers.IO) {
        fileOperationLock.withLock {
            val currentWidgets = _widgets.value?.toMutableList() ?: mutableListOf()
            val updatedWidgets = currentWidgets.map { widget ->
                widget.copy(showPausedFrame = showPausedFrame)
            }

            _widgets.postValue(updatedWidgets)
            saveWidgetsToStorage(updatedWidgets)
            setShowPausedFrameSetting(showPausedFrame)
        }
    }
}
