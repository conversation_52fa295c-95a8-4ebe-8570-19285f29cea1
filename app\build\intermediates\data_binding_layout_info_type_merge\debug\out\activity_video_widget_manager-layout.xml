<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_video_widget_manager" modulePackage="com.enhanced.videowidget" filePath="app\src\main\res\layout\activity_video_widget_manager.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_video_widget_manager_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="245" endOffset="12"/></Target><Target id="@+id/textWidgetName" view="TextView"><Expressions/><location startLine="30" startOffset="16" endLine="37" endOffset="50"/></Target><Target id="@+id/textWidgetDescription" view="TextView"><Expressions/><location startLine="40" startOffset="16" endLine="47" endOffset="48"/></Target><Target id="@+id/imagePreview" view="ImageView"><Expressions/><location startLine="50" startOffset="16" endLine="58" endOffset="66"/></Target><Target id="@+id/buttonPlayPause" view="Button"><Expressions/><location startLine="67" startOffset="20" endLine="76" endOffset="60"/></Target><Target id="@+id/buttonUpdate" view="Button"><Expressions/><location startLine="78" startOffset="20" endLine="87" endOffset="54"/></Target><Target id="@+id/textWidgetDetails" view="TextView"><Expressions/><location startLine="117" startOffset="16" endLine="124" endOffset="192"/></Target><Target id="@+id/switchShowPausedFrame" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="181" startOffset="20" endLine="186" endOffset="48"/></Target><Target id="@+id/buttonDelete" view="Button"><Expressions/><location startLine="226" startOffset="16" endLine="237" endOffset="58"/></Target></Targets></Layout>