package com.enhanced.videowidget.widget;

import java.lang.System;

/**
 * Enhanced Video Widget Provider with pause/play functionality
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0015\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 \u001a2\u00020\u0001:\u0001\u001aB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0018\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0018\u0010\n\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0018\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\rH\u0016J\u0010\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0018\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0012H\u0016J \u0010\u0013\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\f\u001a\u00020\rH\u0016J\u0010\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u0018\u0010\u0017\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\bH\u0002J\u0018\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\bH\u0002\u00a8\u0006\u001b"}, d2 = {"Lcom/enhanced/videowidget/widget/EnhancedVideoWidget;", "Landroid/appwidget/AppWidgetProvider;", "()V", "handlePlayPauseAction", "", "context", "Landroid/content/Context;", "widgetId", "", "handleSettingsAction", "handleWidgetTapAction", "onDeleted", "appWidgetIds", "", "onDisabled", "onEnabled", "onReceive", "intent", "Landroid/content/Intent;", "onUpdate", "appWidgetManager", "Landroid/appwidget/AppWidgetManager;", "requestBatteryOptimizationExemption", "startWidgetUpdateWork", "appWidgetId", "stopWidgetUpdateWork", "Companion", "app_debug"})
public final class EnhancedVideoWidget extends android.appwidget.AppWidgetProvider {
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.widget.EnhancedVideoWidget.Companion Companion = null;
    private static final java.lang.String TAG = "EnhancedVideoWidget";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ACTION_WIDGET_TAP = "com.enhanced.videowidget.ACTION_WIDGET_TAP";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ACTION_PLAY_PAUSE = "com.enhanced.videowidget.ACTION_PLAY_PAUSE";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ACTION_SETTINGS = "com.enhanced.videowidget.ACTION_SETTINGS";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String EXTRA_WIDGET_ID = "widget_id";
    
    public EnhancedVideoWidget() {
        super();
    }
    
    @java.lang.Override
    public void onReceive(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    android.content.Intent intent) {
    }
    
    private final void handlePlayPauseAction(android.content.Context context, int widgetId) {
    }
    
    private final void handleSettingsAction(android.content.Context context, int widgetId) {
    }
    
    private final void handleWidgetTapAction(android.content.Context context, int widgetId) {
    }
    
    @java.lang.Override
    public void onUpdate(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    android.appwidget.AppWidgetManager appWidgetManager, @org.jetbrains.annotations.NotNull
    int[] appWidgetIds) {
    }
    
    @java.lang.Override
    public void onDeleted(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    int[] appWidgetIds) {
    }
    
    @java.lang.Override
    public void onEnabled(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
    }
    
    @java.lang.Override
    public void onDisabled(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
    }
    
    private final void startWidgetUpdateWork(android.content.Context context, int appWidgetId) {
    }
    
    private final void stopWidgetUpdateWork(android.content.Context context, int appWidgetId) {
    }
    
    private final void requestBatteryOptimizationExemption(android.content.Context context) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J6\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\u00102\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0017R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/enhanced/videowidget/widget/EnhancedVideoWidget$Companion;", "", "()V", "ACTION_PLAY_PAUSE", "", "ACTION_SETTINGS", "ACTION_WIDGET_TAP", "EXTRA_WIDGET_ID", "TAG", "setupClickListeners", "", "context", "Landroid/content/Context;", "views", "Landroid/widget/RemoteViews;", "appWidgetId", "", "updateAppWidget", "appWidgetManager", "Landroid/appwidget/AppWidgetManager;", "widget", "Lcom/enhanced/videowidget/data/model/Widget;", "frameBitmap", "Landroid/graphics/Bitmap;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void updateAppWidget(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        android.appwidget.AppWidgetManager appWidgetManager, int appWidgetId, @org.jetbrains.annotations.Nullable
        com.enhanced.videowidget.data.model.Widget widget, @org.jetbrains.annotations.Nullable
        android.graphics.Bitmap frameBitmap) {
        }
        
        private final void setupClickListeners(android.content.Context context, android.widget.RemoteViews views, int appWidgetId) {
        }
    }
}