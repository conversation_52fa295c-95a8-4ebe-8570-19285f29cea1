*com/enhanced/videowidget/data/model/Widget9com/enhanced/videowidget/data/repository/WidgetRepositoryCcom/enhanced/videowidget/data/repository/WidgetRepository$Companion4com/enhanced/videowidget/service/WidgetUpdateService>com/enhanced/videowidget/service/WidgetUpdateService$Companion(com/enhanced/videowidget/ui/MainActivity2com/enhanced/videowidget/ui/MainActivity$Companion6com/enhanced/videowidget/ui/VideoWidgetManagerActivity@com/enhanced/videowidget/ui/VideoWidgetManagerActivity$Companion7com/enhanced/videowidget/ui/WidgetConfigurationActivityAcom/enhanced/videowidget/ui/WidgetConfigurationActivity$Companion5com/enhanced/videowidget/ui/adapter/WidgetListAdapterFcom/enhanced/videowidget/ui/adapter/WidgetListAdapter$WidgetViewHolderHcom/enhanced/videowidget/ui/adapter/WidgetListAdapter$WidgetDiffCallback(com/enhanced/videowidget/utils/AppLogger0com/enhanced/videowidget/utils/PermissionManager:com/enhanced/videowidget/utils/PermissionManager$Companion/com/enhanced/videowidget/utils/VideoFileManager9com/enhanced/videowidget/utils/VideoFileManager$Companion2com/enhanced/videowidget/utils/VideoFrameExtractor<com/enhanced/videowidget/utils/VideoFrameExtractor$Companion<com/enhanced/videowidget/utils/VideoFrameExtractor$FrameInfo@com/enhanced/videowidget/utils/VideoFrameExtractor$VideoMetadata3com/enhanced/videowidget/widget/EnhancedVideoWidget=com/enhanced/videowidget/widget/EnhancedVideoWidget$Companion2com/enhanced/videowidget/worker/WidgetUpdateWorker<com/enhanced/videowidget/worker/WidgetUpdateWorker$Companion.kotlin_module                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 