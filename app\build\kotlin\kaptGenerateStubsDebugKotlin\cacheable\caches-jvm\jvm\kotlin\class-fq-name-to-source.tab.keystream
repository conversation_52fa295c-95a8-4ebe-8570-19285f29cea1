5com.enhanced.videowidget.ui.adapter.WidgetListAdapterFcom.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetViewHolderHcom.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetDiffCallback4com.enhanced.videowidget.service.WidgetUpdateService>com.enhanced.videowidget.service.WidgetUpdateService.Companion9com.enhanced.videowidget.data.repository.WidgetRepositoryCcom.enhanced.videowidget.data.repository.WidgetRepository.Companion(com.enhanced.videowidget.utils.AppLogger0com.enhanced.videowidget.utils.PermissionManager:com.enhanced.videowidget.utils.PermissionManager.Companion/com.enhanced.videowidget.utils.VideoFileManager9com.enhanced.videowidget.utils.VideoFileManager.Companion2com.enhanced.videowidget.utils.VideoFrameExtractor<<EMAIL><com.enhanced.videowidget.utils.VideoFrameExtractor.Companion(<EMAIL><com.enhanced.videowidget.worker.WidgetUpdateWorker.Companion3com.enhanced.videowidget.widget.EnhancedVideoWidget=com.enhanced.videowidget.widget.EnhancedVideoWidget.Companion*com.enhanced.videowidget.data.model.Widget                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                