package com.enhanced.videowidget.ui

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.utils.AppLogger
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * Activity for managing a specific video widget
 * Provides options to play, pause, update, and view details of a widget
 */
class VideoWidgetManagerActivity : AppCompatActivity() {
    
    companion object {
        const val EXTRA_WIDGET_ID = "extra_widget_id"
        private const val UPDATE_WIDGET_REQUEST = 1001
    }
    
    private lateinit var repository: WidgetRepository
    private var widgetId: Int = -1
    private var currentWidget: Widget? = null
    
    // UI Components
    private lateinit var textWidgetName: TextView
    private lateinit var textWidgetDescription: TextView
    private lateinit var textWidgetDetails: TextView
    private lateinit var imagePreview: ImageView
    private lateinit var buttonPlayPause: Button
    private lateinit var buttonUpdate: Button
    private lateinit var buttonDelete: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_video_widget_manager)
        
        // Setup toolbar with back button
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "Widget Manager"
        }
        
        // Get widget ID from intent
        widgetId = intent.getIntExtra(EXTRA_WIDGET_ID, -1)
        if (widgetId == -1) {
            AppLogger.e(AppLogger.TAG_CONFIG, "No widget ID provided to VideoWidgetManagerActivity")
            finish()
            return
        }
        
        setupRepository()
        setupUI()
        loadWidgetData()
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun setupRepository() {
        repository = WidgetRepository.getInstance()
        repository.initialize(this)
    }
    
    private fun setupUI() {
        // Initialize UI components
        textWidgetName = findViewById(R.id.textWidgetName)
        textWidgetDescription = findViewById(R.id.textWidgetDescription)
        textWidgetDetails = findViewById(R.id.textWidgetDetails)
        imagePreview = findViewById(R.id.imagePreview)
        buttonPlayPause = findViewById(R.id.buttonPlayPause)
        buttonUpdate = findViewById(R.id.buttonUpdate)
        buttonDelete = findViewById(R.id.buttonDelete)
        
        // Setup button click listeners
        buttonPlayPause.setOnClickListener { handlePlayPauseClick() }
        buttonUpdate.setOnClickListener { handleUpdateClick() }
        buttonDelete.setOnClickListener { handleDeleteClick() }
    }
    
    private fun loadWidgetData() {
        lifecycleScope.launch {
            try {
                val widget = repository.getWidget(widgetId)
                if (widget != null) {
                    currentWidget = widget
                    updateUI(widget)
                    loadPreviewImage(widget)
                } else {
                    AppLogger.e(AppLogger.TAG_CONFIG, "Widget $widgetId not found")
                    Toast.makeText(this@VideoWidgetManagerActivity, "Widget not found", Toast.LENGTH_SHORT).show()
                    finish()
                }
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_CONFIG, "Error loading widget $widgetId", e)
                Toast.makeText(this@VideoWidgetManagerActivity, "Error loading widget", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    private fun updateUI(widget: Widget) {
        textWidgetName.text = widget.name
        textWidgetDescription.text = widget.description
        
        // Format widget details
        val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        val createdDate = dateFormat.format(Date(widget.createdAt))
        val updatedDate = dateFormat.format(Date(widget.lastUpdated))
        
        val details = buildString {
            appendLine("Widget ID: ${widget.id}")
            appendLine("Media Type: ${widget.mediaType}")
            appendLine("Total Frames: ${widget.totalFrames}")
            appendLine("Frame Rate: ${widget.framerate} fps")
            appendLine("Current Frame: ${widget.currentFrameIndex + 1}/${widget.totalFrames}")
            appendLine("Created: $createdDate")
            appendLine("Last Updated: $updatedDate")
            if (!widget.videoPath.isNullOrEmpty()) {
                appendLine("Video Path: ${widget.videoPath}")
            }
            if (!widget.framesDirectory.isNullOrEmpty()) {
                appendLine("Frames Directory: ${widget.framesDirectory}")
            }
        }
        textWidgetDetails.text = details
        
        // Update play/pause button
        updatePlayPauseButton(widget)
    }
    
    private fun updatePlayPauseButton(widget: Widget) {
        when {
            widget.isPaused -> {
                buttonPlayPause.text = "Resume"
                buttonPlayPause.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_play_arrow, 0, 0, 0)
            }
            widget.isPlaying -> {
                buttonPlayPause.text = "Pause"
                buttonPlayPause.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_pause, 0, 0, 0)
            }
            else -> {
                buttonPlayPause.text = "Play"
                buttonPlayPause.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_play_arrow, 0, 0, 0)
            }
        }
    }
    
    private fun loadPreviewImage(widget: Widget) {
        lifecycleScope.launch {
            try {
                val previewBitmap = repository.loadFrameBitmap(widget.id, 0) // Load first frame
                if (previewBitmap != null) {
                    imagePreview.setImageBitmap(previewBitmap)
                } else {
                    imagePreview.setImageResource(R.drawable.ic_video_placeholder)
                }
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_CONFIG, "Error loading preview for widget ${widget.id}", e)
                imagePreview.setImageResource(R.drawable.ic_video_placeholder)
            }
        }
    }
    
    private fun handlePlayPauseClick() {
        currentWidget?.let { widget ->
            lifecycleScope.launch {
                try {
                    val updatedWidget = repository.toggleWidgetPlayPause(widget.id)
                    updatedWidget?.let { 
                        currentWidget = it
                        updatePlayPauseButton(it)
                        
                        // Trigger widget update to handle WorkManager start/stop
                        triggerWidgetUpdate(it)
                        
                        val message = if (it.isPaused) {
                            "Widget Paused (CPU optimized)"
                        } else if (it.isPlaying) {
                            "Widget Playing"
                        } else {
                            "Widget Stopped"
                        }
                        Toast.makeText(this@VideoWidgetManagerActivity, message, Toast.LENGTH_SHORT).show()
                    }
                } catch (e: Exception) {
                    AppLogger.e(AppLogger.TAG_CONFIG, "Error toggling widget playback", e)
                    Toast.makeText(this@VideoWidgetManagerActivity, "Error updating widget", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun handleUpdateClick() {
        currentWidget?.let { widget ->
            val intent = Intent(this, WidgetConfigurationActivity::class.java).apply {
                putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, widget.id)
                putExtra("is_update", true)
            }
            startActivityForResult(intent, UPDATE_WIDGET_REQUEST)
        }
    }
    
    private fun handleDeleteClick() {
        currentWidget?.let { widget ->
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Delete Widget")
                .setMessage("Are you sure you want to delete '${widget.name}'? This action cannot be undone.")
                .setPositiveButton("Delete") { _, _ ->
                    deleteWidget(widget)
                }
                .setNegativeButton("Cancel", null)
                .show()
        }
    }
    
    private fun deleteWidget(widget: Widget) {
        lifecycleScope.launch {
            try {
                repository.deleteWidget(widget.id)
                Toast.makeText(this@VideoWidgetManagerActivity, "Widget deleted", Toast.LENGTH_SHORT).show()
                finish()
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_CONFIG, "Error deleting widget ${widget.id}", e)
                Toast.makeText(this@VideoWidgetManagerActivity, "Error deleting widget", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun triggerWidgetUpdate(widget: Widget) {
        try {
            val appWidgetManager = AppWidgetManager.getInstance(this)
            val widgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(this, EnhancedVideoWidget::class.java)
            )
            
            if (widgetIds.contains(widget.id)) {
                EnhancedVideoWidget().onUpdate(this, appWidgetManager, intArrayOf(widget.id))
            }
        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_CONFIG, "Error triggering widget update", e)
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UPDATE_WIDGET_REQUEST && resultCode == RESULT_OK) {
            // Reload widget data after update
            loadWidgetData()
        }
    }
}
