08-14 07:29:44.828   604   639 I ActivityManager: Start proc 3420:com.enhanced.videowidget/u0a231 for service {com.enhanced.videowidget/androidx.work.impl.background.systemjob.SystemJobService}
08-14 07:29:44.831  3420  3420 I ced.videowidget: Late-enabling -Xcheck:jni
08-14 07:29:44.845  3420  3420 I ced.videowidget: Using CollectorTypeCMC GC.
08-14 07:29:44.845  3420  3420 W ced.videowidget: Unexpected CPU variant for x86: x86_64.
08-14 07:29:44.845  3420  3420 W ced.videowidget: Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, default
08-14 07:29:44.905  3420  3420 W ziparchive: Unable to open '/data/app/~~tb6ekNhYqJ6bEX2WH7kRvw==/com.enhanced.videowidget-kKc_H5bpXHobmpTahVkjig==/base.dm': No such file or directory
08-14 07:29:44.905  3420  3420 W ziparchive: Unable to open '/data/app/~~tb6ekNhYqJ6bEX2WH7kRvw==/com.enhanced.videowidget-kKc_H5bpXHobmpTahVkjig==/base.dm': No such file or directory
08-14 07:29:45.037  3420  3420 D nativeloader: Configuring clns-7 for other apk /data/app/~~tb6ekNhYqJ6bEX2WH7kRvw==/com.enhanced.videowidget-kKc_H5bpXHobmpTahVkjig==/base.apk. target_sdk_version=33, uses_libraries=, library_path=/data/app/~~tb6ekNhYqJ6bEX2WH7kRvw==/com.enhanced.videowidget-kKc_H5bpXHobmpTahVkjig==/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.enhanced.videowidget
08-14 07:29:45.253   604  1643 W JobScheduler: Job didn't exist in JobStore: cdfb8bb #u0a231/6 com.enhanced.videowidget/androidx.work.impl.background.systemjob.SystemJobService
08-14 07:29:45.836  3420  3420 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 07:29:45.843  3420  3420 D videowidget-repo: Widget JSON content: [{"createdAt":1755136682682,"currentFrameIndex":26,"description":"Duration: 6s","firstFrame":{"mHeight":300,"mNativePtr":138414593220224,"mWidth":300},"framerate":30.0,"framesDirectory":"/data/user/0/com.enhanced.videowidget/files/widget_frames/widget_25","id":25,"isPaused":false,"isPlaying":true,"lastUpdated":1755136784742,"mediaType":"video","name":"Test","totalFrames":100,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/7723f591-15e2-464c-bd5d-acaffff72d6b.mp4"}]
08-14 07:29:45.861  3420  3420 D videowidget-repo: Loaded 1 widgets from storage
08-14 07:29:45.911  3420  3420 D videowidget-repo: getWidget(25) - Found: true, Total widgets: 1
08-14 07:29:45.911  3420  3420 D videowidget-repo: Widget 25 details - isPlaying: true, isPaused: false, totalFrames: 100, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/7723f591-15e2-464c-bd5d-acaffff72d6b.mp4
08-14 07:29:45.953   604   795 D AppWidgetServiceImpl: Trying to notify widget update for package com.enhanced.videowidget with widget id: 25
08-14 07:29:51.015  3420  3441 D ProfileInstaller: Installing profile for com.enhanced.videowidget
08-14 07:29:53.493   604  1643 V SplashScreenExceptionList: SplashScreen checking exception for package com.enhanced.videowidget (target sdk:33) -> false
08-14 07:29:53.502  4615  4635 V WindowManagerShell: Transition requested (#478): android.os.BinderProxy@26b8945 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=108 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.enhanced.videowidget/.ui.MainActivity } baseActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity} topActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity} origActivity=null realActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity} numActivities=1 lastActiveTime=21520973 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@bfaa89a} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 136 - 0, 0) topActivityInfo=ActivityInfo{a34e5cb com.enhanced.videowidget.ui.MainActivity} launchCookies=[android.os.BinderProxy@c8be1a8] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= true topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2340 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden freeformCameraCompatMode=inactive}}}, pipTask = null, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@64074c1, appThread = android.app.IApplicationThread$Stub$Proxy@b4ab466, debugName = QuickstepLaunch }, displayChange = null, flags = 0, debugId = 478 }
08-14 07:29:53.502   604  1643 I ActivityTaskManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.enhanced.videowidget/.ui.MainActivity bnds=[325,1788][507,1993]} with LAUNCH_MULTIPLE from uid 10186 (BAL_ALLOW_VISIBLE_WINDOW) result code=0
08-14 07:29:53.534  1114  1114 I TopTaskTracker: onTaskMovedToFront: (moved taskInfo to front) taskId=108, baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.enhanced.videowidget/.ui.MainActivity }
08-14 07:29:53.555   604  1643 D CoreBackPreview: Window{822fc05 u0 Splash Screen com.enhanced.videowidget}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@9dc5381, mPriority=0, mIsAnimationCallback=false}
08-14 07:29:53.819   604   626 V WindowManager: Sent Transition (#478) createdAt=08-14 07:29:53.485 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=108 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.enhanced.videowidget/.ui.MainActivity } baseActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity} topActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity} origActivity=null realActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity} numActivities=1 lastActiveTime=21520973 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{3c2d780 Task{6316449 #108 type=standard A=10231:com.enhanced.videowidget}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 136 - 0, 0) topActivityInfo=ActivityInfo{49ef1b9 com.enhanced.videowidget.ui.MainActivity} launchCookies=[android.os.BinderProxy@36979fe] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= true topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2340 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden freeformCameraCompatMode=inactive}}}, pipTask = null, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@f88855f, appThread = android.app.IApplicationThread$Stub$Proxy@c3debac, debugName = QuickstepLaunch }, displayChange = null, flags = 0, debugId = 478 }
08-14 07:29:53.821   604   626 V WindowManager:         {WCT{RemoteToken{3c2d780 Task{6316449 #108 type=standard A=10231:com.enhanced.videowidget}}} m=OPEN f=NONE leash=Surface(name=Task=108)/@0xdc7c4b2 sb=Rect(0, 0 - 1080, 2340) eb=Rect(0, 0 - 1080, 2340) d=0 taskParent=-1},
08-14 07:29:53.849  3420  3420 W ced.videowidget: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (unsupported, reflection, allowed)
08-14 07:29:53.850  3420  3420 W ced.videowidget: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
08-14 07:29:54.069  3420  3420 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 07:29:54.071  3420  3420 D videowidget-repo: Widget JSON content: [{"createdAt":1755136682682,"currentFrameIndex":26,"description":"Duration: 6s","firstFrame":{"mHeight":300,"mNativePtr":138414593220224,"mWidth":300},"framerate":30.0,"framesDirectory":"/data/user/0/com.enhanced.videowidget/files/widget_frames/widget_25","id":25,"isPaused":true,"isPlaying":false,"lastUpdated":1755136785911,"mediaType":"video","name":"Test","totalFrames":100,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/7723f591-15e2-464c-bd5d-acaffff72d6b.mp4"}]
08-14 07:29:54.073  3420  3420 D videowidget-repo: Loaded 1 widgets from storage
08-14 07:29:54.090  3420  3420 E TransactionExecutor: tId:548595207     LaunchActivityItem{activityToken=android.os.BinderProxy@21029be,intent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.enhanced.videowidget/.ui.MainActivity bnds=[325,1788][507,1993] },ident=197409360,info=ActivityInfo{8acf14a com.enhanced.videowidget.ui.MainActivity},curConfig={1.0 ?mcc0mnc [en_US] ldltr sw393dp w393dp h753dp 440dpi nrml long port finger qwerty/v/v dpad/v winConfig={ mBounds=Rect(0, 0 - 1080, 2340) mAppBounds=Rect(0, 136 - 1080, 2208) mMaxBounds=Rect(0, 0 - 1080, 2340) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0} as.2 s.8 fontWeightAdjustment=0},overrideConfig={1.0 ?mcc0mnc [en_US] ldltr sw393dp w393dp h753dp 440dpi nrml long port finger qwerty/v/v dpad/v winConfig={ mBounds=Rect(0, 0 - 1080, 2340) mAppBounds=Rect(0, 136 - 1080, 2208) mMaxBounds=Rect(0, 0 - 1080, 2340) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=standard mAlwaysOnTop=undefined mRotation=ROTATION_0} as.2 s.4 fontWeightAdjustment=0},deviceId=0,referrer=com.google.android.apps.nexuslauncher,procState=19,state=null,persistentState=null,pendingResults=null,pendingNewIntents=null,sceneTransitionInfo=null,profilerInfo=null,assistToken=android.os.BinderProxy@cd0619f,shareableActivityToken=android.os.BinderProxy@27232ec,activityWindowInfo=ActivityWindowInfo{isEmbedded=false, taskBounds=Rect(0, 0 - 1080, 2340), taskFragmentBounds=Rect(0, 0 - 1080, 2340)}}
08-14 07:29:54.090  3420  3420 E TransactionExecutor: tId:548595207     Target activity: com.enhanced.videowidget.ui.MainActivity
08-14 07:29:54.099  3420  3420 E AndroidRuntime: Process: com.enhanced.videowidget, PID: 3420
08-14 07:29:54.099  3420  3420 E AndroidRuntime: java.lang.RuntimeException: Unable to start activity ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.MainActivity}: java.lang.IllegalStateException: This Activity already has an action bar supplied by the window decor. Do not request Window.FEATURE_SUPPORT_ACTION_BAR and set windowActionBar to false in your theme to use a Toolbar instead.
08-14 07:29:54.099  3420  3420 E AndroidRuntime:        at com.enhanced.videowidget.ui.MainActivity.setupUI(MainActivity.kt:66)
08-14 07:29:54.099  3420  3420 E AndroidRuntime:        at com.enhanced.videowidget.ui.MainActivity.onCreate(MainActivity.kt:55)
08-14 07:29:54.115   604   937 W ActivityManager: Process com.enhanced.videowidget has crashed too many times, killing! Reason: crashed quickly
08-14 07:29:54.115   604   937 W ActivityTaskManager:   Force finishing activity com.enhanced.videowidget/.ui.MainActivity
08-14 07:29:54.134   604   937 I ActivityManager: Killing 3420:com.enhanced.videowidget/u0a231 (adj 0): crash
08-14 07:29:54.156   604  1696 V ActivityManager: Got obituary of 3420:com.enhanced.videowidget
08-14 07:29:54.286   604  1978 D CoreBackPreview: Window{822fc05 u0 Splash Screen com.enhanced.videowidget EXITING}: Setting back callback null
08-14 07:29:54.286   604  1695 W InputManager-JNI: Input channel object '822fc05 Splash Screen com.enhanced.videowidget (client)' was disposed without first being removed with the input manager!