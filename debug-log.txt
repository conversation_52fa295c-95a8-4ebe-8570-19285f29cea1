08-14 07:43:31.116   604   679 V SplashScreenExceptionList: SplashScreen checking exception for package com.enhanced.videowidget (target sdk:33) -> false
08-14 07:43:31.119  4615  4635 V WindowManagerShell: Transition requested (#504): android.os.BinderProxy@8e506ad TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=114 displayId=0 isRunning=true baseIntent=Intent { flg=0x14000000 cmp=com.enhanced.videowidget/.ui.VideoWidgetManagerActivity } baseActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} topActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} origActivity=null realActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} numActivities=1 lastActiveTime=22338591 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@85abde2} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 136 - 0, 0) topActivityInfo=ActivityInfo{349e573 com.enhanced.videowidget.ui.VideoWidgetManagerActivity} launchCookies=[android.os.BinderProxy@451ec30] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= true topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2340 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden freeformCameraCompatMode=inactive}}}, pipTask = null, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@9f860a9, appThread = android.app.IApplicationThread$Stub$Proxy@7ead62e, debugName = QuickstepLaunch }, displayChange = null, flags = 0, debugId = 504 }
08-14 07:43:31.119   604   679 I ActivityTaskManager: START u0 {flg=0x14000000 cmp=com.enhanced.videowidget/.ui.VideoWidgetManagerActivity (has extras)} with LAUNCH_MULTIPLE from uid 10232 (realCallingUid=10186) (BAL_ALLOW_VISIBLE_WINDOW) result code=0
08-14 07:43:31.130  1114  1114 I TopTaskTracker: onTaskMovedToFront: (moved taskInfo to front) taskId=114, baseIntent=Intent { flg=0x14000000 cmp=com.enhanced.videowidget/.ui.VideoWidgetManagerActivity }
08-14 07:43:31.136   604   679 D CoreBackPreview: Window{641c41c u0 Splash Screen com.enhanced.videowidget}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@6865dfa, mPriority=0, mIsAnimationCallback=false}
08-14 07:43:31.154  4629  4629 I ced.videowidget: Late-enabling -Xcheck:jni
08-14 07:43:31.155   604   639 I ActivityManager: Start proc 4629:com.enhanced.videowidget/u0a232 for next-top-activity {com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity}
08-14 07:43:31.170  4629  4629 I ced.videowidget: Using CollectorTypeCMC GC.
08-14 07:43:31.171  4629  4629 W ced.videowidget: Unexpected CPU variant for x86: x86_64.
08-14 07:43:31.171  4629  4629 W ced.videowidget: Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, default
08-14 07:43:31.214  4629  4629 W ziparchive: Unable to open '/data/app/~~PEKajkaNp3km_XxwXiREPQ==/com.enhanced.videowidget-3jfTamc_VhffjnyfZGTijw==/base.dm': No such file or directory
08-14 07:43:31.219  4629  4629 W ziparchive: Unable to open '/data/app/~~PEKajkaNp3km_XxwXiREPQ==/com.enhanced.videowidget-3jfTamc_VhffjnyfZGTijw==/base.dm': No such file or directory
08-14 07:43:31.247   604   626 V WindowManager: Sent Transition (#504) createdAt=08-14 07:43:31.111 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=114 displayId=0 isRunning=true baseIntent=Intent { flg=0x14000000 cmp=com.enhanced.videowidget/.ui.VideoWidgetManagerActivity } baseActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} topActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} origActivity=null realActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} numActivities=1 lastActiveTime=22338591 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{5987ab4 Task{21dca2 #114 type=standard A=10232:com.enhanced.videowidget}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 136 - 0, 0) topActivityInfo=ActivityInfo{28746dd com.enhanced.videowidget.ui.VideoWidgetManagerActivity} launchCookies=[android.os.BinderProxy@71eb252] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= true topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2340 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden freeformCameraCompatMode=inactive}}}, pipTask = null, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@fd31923, appThread = android.app.IApplicationThread$Stub$Proxy@29c8e20, debugName = QuickstepLaunch }, displayChange = null, flags = 0, debugId = 504 }
08-14 07:43:31.247   604   626 V WindowManager:         {WCT{RemoteToken{5987ab4 Task{21dca2 #114 type=standard A=10232:com.enhanced.videowidget}}} m=OPEN f=NONE leash=Surface(name=Task=114)/@0xdcc5fc6 sb=Rect(0, 0 - 1080, 2340) eb=Rect(0, 0 - 1080, 2340) d=0 taskParent=-1},
08-14 07:43:31.356  4629  4629 D nativeloader: Configuring clns-7 for other apk /data/app/~~PEKajkaNp3km_XxwXiREPQ==/com.enhanced.videowidget-3jfTamc_VhffjnyfZGTijw==/base.apk. target_sdk_version=33, uses_libraries=, library_path=/data/app/~~PEKajkaNp3km_XxwXiREPQ==/com.enhanced.videowidget-3jfTamc_VhffjnyfZGTijw==/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.enhanced.videowidget
08-14 07:43:31.904  4629  4629 W ced.videowidget: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (unsupported, reflection, allowed)
08-14 07:43:31.905  4629  4629 W ced.videowidget: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
08-14 07:43:32.101  4629  4629 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 07:43:32.107  4629  4629 D videowidget-repo: Widget JSON content: [{"createdAt":1755137227397,"currentFrameIndex":99,"description":"Duration: 6s","firstFrame":{"mHeight":300,"mNativePtr":138414593218784,"mWidth":300},"framerate":30.0,"framesDirectory":"/data/user/0/com.enhanced.videowidget/files/widget_frames/widget_26","id":26,"isPaused":true,"isPlaying":false,"lastUpdated":1755137274232,"mediaType":"video","name":"test","showPausedFrame":true,"totalFrames":100,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/3f2918e9-ccc1-4583-a73a-49b654c45908.mp4"}]
08-14 07:43:32.124  4629  4629 D videowidget-repo: Loaded 1 widgets from storage
08-14 07:43:32.180  4629  4629 D videowidget-repo: getWidget(26) - Found: false, Total widgets: 0
08-14 07:43:32.182  4629  4629 E videowidget-config: Widget 26 not found
08-14 07:43:32.189  4615  4635 V WindowManagerShell: Transition requested (#505): android.os.BinderProxy@d2df345 TransitionRequestInfo { type = CLOSE, triggerTask = TaskInfo{userId=0 taskId=114 displayId=0 isRunning=false baseIntent=Intent { flg=0x14000000 cmp=com.enhanced.videowidget/.ui.VideoWidgetManagerActivity } baseActivity=null topActivity=null origActivity=null realActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} numActivities=0 lastActiveTime=22339050 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@90dca9a} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 136 - 0, 0) topActivityInfo=null launchCookies=[android.os.BinderProxy@451ec30] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden freeformCameraCompatMode=inactive}}}, pipTask = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 505 }
08-14 07:43:32.226  4629  4629 D videowidget-service: WidgetUpdateService created
08-14 07:43:32.229  4629  4629 D videowidget-service: onStartCommand: null
08-14 07:43:32.456   604   626 V WindowManager: Sent Transition (#505) createdAt=08-14 07:43:32.188 via request=TransitionRequestInfo { type = CLOSE, triggerTask = TaskInfo{userId=0 taskId=114 displayId=0 isRunning=false baseIntent=Intent { flg=0x14000000 cmp=com.enhanced.videowidget/.ui.VideoWidgetManagerActivity } baseActivity=null topActivity=null origActivity=null realActivity=ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.ui.VideoWidgetManagerActivity} numActivities=0 lastActiveTime=22339050 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{5987ab4 Task{21dca2 #114 type=standard A=10232:com.enhanced.videowidget}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 136 - 0, 0) topActivityInfo=null launchCookies=[android.os.BinderProxy@71eb252] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden freeformCameraCompatMode=inactive}}}, pipTask = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 505 }
08-14 07:43:32.456   604   626 V WindowManager:         {WCT{RemoteToken{5987ab4 Task{21dca2 #114 type=standard A=10232:com.enhanced.videowidget}}} m=CLOSE f=NONE leash=Surface(name=Task=114)/@0xdcc5fc6 sb=Rect(0, 0 - 1080, 2340) eb=Rect(0, 0 - 1080, 2340) d=0 taskParent=-1},
08-14 07:43:32.635  4629  4636 I ced.videowidget: Compiler allocated 4219KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
08-14 07:43:33.151   604  1696 W InputManager-JNI: Input channel object '641c41c Splash Screen com.enhanced.videowidget (client)' was disposed without first being removed with the input manager!
08-14 07:43:34.790   604  1696 W NotificationService: Toast already killed. pkg=com.enhanced.videowidget token=android.os.BinderProxy@84794ba