08-14 06:30:44.295 16368 16368 E FrameTracker: force finish cuj, time out: J<IME_INSETS_SHOW_ANIMATION::0@<EMAIL>>
08-14 06:30:53.552 16368 16368 I videowidget-config: createWidget called - videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:53.552 16368 16368 D videowidget-config: Video path validated: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:53.557 16368 16368 I videowidget-config: Progress: Creating widget...
08-14 06:30:53.559 16368 16368 I videowidget-config: Starting widget creation process
08-14 06:30:53.559 16368 16368 I videowidget-config: Progress: Verifying video file...
08-14 06:30:53.560 16368 16463 D videowidget-video: Getting video metadata for: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:53.560 16368 16463 D videowidget-video: File validation passed - Size: 836471 bytes
08-14 06:30:53.561 16368 16463 D videowidget-video: Setting data source...
08-14 06:30:53.587 16368 16463 D videowidget-video: Data source set successfully
08-14 06:30:53.590 16368 16463 D videowidget-video: Raw metadata - Duration: '6100', Width: '1080', Height: '864', FrameRate: 'null', MimeType: 'video/mp4', HasVideo: 'yes'
08-14 06:30:53.590 16368 16463 I videowidget-video: === VIDEO METADATA ===
08-14 06:30:53.591 16368 16463 I videowidget-video: Duration: 6100ms (6s)
08-14 06:30:53.591 16368 16463 I videowidget-video: Frame Rate: 30.0 fps
08-14 06:30:53.591 16368 16463 I videowidget-video: Resolution: 1080x864
08-14 06:30:53.591 16368 16463 D videowidget-video: === VIDEO METADATA ===
08-14 06:30:53.592 16368 16463 D videowidget-video: Duration: 6100ms (6s)
08-14 06:30:53.592 16368 16463 D videowidget-video: Frame Rate: 30.0 fps
08-14 06:30:53.592 16368 16463 D videowidget-video: Resolution: 1080x864
08-14 06:30:53.593 16368 16463 I videowidget-video: Video metadata extraction successful - MimeType: video/mp4
08-14 06:30:53.595 16368 16463 D videowidget-video: MediaMetadataRetriever released
08-14 06:30:53.596 16368 16368 D videowidget-config: Video metadata verified for widget creation
08-14 06:30:53.596 16368 16368 I videowidget-config: Progress: Saving widget configuration...
08-14 06:30:53.598 16368 16368 D videowidget-config: Widget object created: Widget(id=23, name=Assa, description=Duration: 6s, firstFrame=null, framesDirectory=null, framerate=30.0, mediaType=video, totalFrames=0, currentFrameIndex=0, isPlaying=true, isPaused=false, videoPath=/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4, createdAt=1755133253598, lastUpdated=1755133253598) 08-14 06:30:53.622 16368 16368 D videowidget-config: Widget saved to repository
08-14 06:30:53.622 16368 16368 I videowidget-config: Progress: Initializing widget display...
08-14 06:30:53.633 16368 16368 I videowidget-config: Widget display updated successfully
08-14 06:30:53.635 16368 16368 D videowidget-config: Manually triggering widget update to start WorkManager
08-14 06:30:53.635 16368 16368 I videowidget-widget: === WIDGET UPDATE CALLED ===
08-14 06:30:53.635 16368 16368 D videowidget-widget: Updating widgets: [23]
08-14 06:30:53.635 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:53.636   604   795 D AppWidgetServiceImpl: Trying to notify widget update for package com.enhanced.videowidget with widget id: 23
08-14 06:30:53.642 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:53.652 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:53.652 16368 16368 D videowidget-widget: Repository initialized for widget updates
08-14 06:30:53.653 16368 16368 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:53.653 16368 16368 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:53.653 16368 16368 D videowidget-widget: Widget 23 found - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:53.658 16368 16368 D videowidget-widget: Widget 23 shouldStartProcessing: true
08-14 06:30:53.658 16368 16368 I videowidget-widget: Starting WorkManager for widget 23
08-14 06:30:53.658   604   795 D AppWidgetServiceImpl: Trying to notify widget update for package com.enhanced.videowidget with widget id: 23
08-14 06:30:53.659 16368 16368 D videowidget-widget: === STARTING WORKMANAGER for Widget 23 ===
08-14 06:30:53.732 16368 16368 D videowidget-widget: WorkRequest created for widget 23
08-14 06:30:53.734   604   619 I ActivityManager: Background started FGS: Allowed [callingPackage: com.enhanced.videowidget; callingUid: 10230; uidState: TOP ; uidBFSL: [BFSL]; intent: Intent { act=com.enhanced.videowidget.ACTION_START_UPDATES cmp=com.enhanced.videowidget/.service.WidgetUpdateService }; code:PROC_STATE_TOP; tempAllowListReason:<,reasonCode:SYSTEM_ALLOW_LISTED,duration:9223372036854775807,callingUid:-1>; targetSdkVersion:33; callerTargetSdkVersion:33; startForegroundCount:0; bindFromPackage:null: isBindService:false]
08-14 06:30:53.735 16368 16368 D videowidget-widget: Foreground service started for widget 23
08-14 06:30:53.743 16368 16368 D videowidget-widget: Cancelled existing work for widget 23
08-14 06:30:53.748 16368 16368 D videowidget-widget: WorkRequest enqueued for widget 23
08-14 06:30:53.814 16368 16368 D videowidget-widget: WorkManager status for widget 23: 1 work items
08-14 06:30:53.814 16368 16368 D videowidget-widget: Work 17c472df-fb96-41d0-8f4f-21b33f92a95c: state=ENQUEUED, tags=[com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23]
08-14 06:30:53.842 16368 16368 D videowidget-service: WidgetUpdateService created
08-14 06:30:53.847 16368 16368 D videowidget-service: onStartCommand: com.enhanced.videowidget.ACTION_START_UPDATES
08-14 06:30:53.868   604   619 W ActivityManager: Starting FGS with type dataSync code=4 callerApp=ProcessRecord{1e409d8 16368:com.enhanced.videowidget/u0a230} targetSDK=33 requiredPermissions=all of the permissions allOf=true [android.permission.FOREGROUND_SERVICE_DATA_SYNC]
08-14 06:30:54.042 16368 16463 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:54.042 16368 16463 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:54.042 16368 16463 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:54.065 16368 16463 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:54.068 16368 16463 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:54.069 16368 16463 D videowidget-worker: Repository initialized for widget 23
08-14 06:30:54.070 16368 16463 D videowidget-worker: === PROCESSING WIDGET FRAMES for Widget 23 ===
08-14 06:30:54.070 16368 16463 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:54.070 16368 16463 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:54.070 16368 16463 D videowidget-worker: Widget found - Video: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4, Total frames: 0, Frames dir: null
08-14 06:30:54.071 16368 16463 D videowidget-worker: Widget state - isPlaying: true, isPaused: false, mediaType: video
08-14 06:30:54.071 16368 16463 D videowidget-worker: shouldStartProcessing: true, shouldAnimate: false
08-14 06:30:54.071 16368 16463 D videowidget-worker: Widget 23 needs frame extraction
08-14 06:30:54.071 16368 16463 I videowidget-worker: === EXTRACTING VIDEO FRAMES for Widget 23 ===
08-14 06:30:54.071 16368 16463 D videowidget-worker: Video path: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:54.074 16368 16463 I videowidget-frames: === STARTING FRAME EXTRACTION ===
08-14 06:30:54.077 16368 16463 D videowidget-frames: Video path: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:54.079 16368 16463 D videowidget-frames: Output directory: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23
08-14 06:30:54.080 16368 16463 D videowidget-frames: Max frames: 100, Target size: 300x300
08-14 06:30:54.083 16368 16463 D videowidget-frames: Setting data source...
08-14 06:30:54.095 16368 16463 D videowidget-frames: Data source set successfully
08-14 06:30:54.097 16368 16463 D videowidget-frames: Raw duration string: '6100'
08-14 06:30:54.097 16368 16463 D videowidget-frames: Parsed duration: 6100ms
08-14 06:30:54.097 16368 16463 I videowidget-frames: Video duration: 6100ms (6s)
08-14 06:30:54.097 16368 16463 I videowidget-frames: Frame interval: 61ms
08-14 06:30:54.098 16368 16463 I videowidget-frames: Will extract 100 frames
08-14 06:30:54.099 16368 16463 D videowidget-frames: Output directory: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23
08-14 06:30:54.099 16368 16463 D videowidget-frames: Output directory already exists
08-14 06:30:54.100 16368 16463 I videowidget-frames: Starting frame extraction loop...
08-14 06:30:54.102 16368 16463 V videowidget-frames: Extracting frame 0 at timestamp 0μs (0ms)
08-14 06:30:54.305 16368 16463 V videowidget-frames: Frame 0 extracted successfully - Size: 1080x864
08-14 06:30:54.306 16368 16463 V videowidget-frames: Frame 0 resized to: 300x300
08-14 06:30:54.306 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_0.png
08-14 06:30:54.400 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:54.400 16368 16463 V videowidget-frames: Frame 0 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_0.png
08-14 06:30:54.401 16368 16463 V videowidget-frames: Frame 0/100 - Success: true
08-14 06:30:54.401 16368 16463 V videowidget-frames: Frame 0/100 - Success: true
08-14 06:30:54.402 16368 16463 V videowidget-frames: Extracting frame 1 at timestamp 61000μs (61ms)
08-14 06:30:54.623 16368 16463 V videowidget-frames: Frame 1 extracted successfully - Size: 1080x864
08-14 06:30:54.624 16368 16463 V videowidget-frames: Frame 1 resized to: 300x300
08-14 06:30:54.624 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_1.png
08-14 06:30:54.649  4615  4622 W ndroid.systemui: ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~m_IbanYOlSlcFtU1vdGGqg==/com.enhanced.videowidget-vr7sJse5OYJY1Tr6gb5Smg==/base.apk' with 3 weak references
08-14 06:30:54.659 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:54.660 16368 16463 V videowidget-frames: Frame 1 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_1.png
08-14 06:30:54.660 16368 16463 V videowidget-frames: Frame 1/100 - Success: true
08-14 06:30:54.660 16368 16463 V videowidget-frames: Frame 1/100 - Success: true
08-14 06:30:54.661 16368 16463 V videowidget-frames: Extracting frame 2 at timestamp 122000μs (122ms)
08-14 06:30:54.766   604  1700 D CoreBackPreview: Window{e98680c u0 com.enhanced.videowidget/com.enhanced.videowidget.ui.WidgetConfigurationActivity}: Setting back callback null
08-14 06:30:54.772   604  1700 W InputManager-JNI: Input channel object 'e98680c com.enhanced.videowidget/com.enhanced.videowidget.ui.WidgetConfigurationActivity (client)' was disposed without first being removed with the input manager!
08-14 06:30:54.801  1114  1188 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.enhanced.videowidget]
08-14 06:30:54.805  1320  1320 I AiAiEcho: AppFetcherImpl onPackageChanged com.enhanced.videowidget.
08-14 06:30:54.810  1320 16296 I AiAiEcho: AppIndexer Package:[com.enhanced.videowidget] UserProfile:[0] Enabled:[true].
08-14 06:30:54.810  1320 16296 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.enhanced.videowidget], userId:[0], reason:[package is updated.].
08-14 06:30:54.810   604   795 I AppWidgetServiceImpl: cancelBroadcastsLocked() for Provider{ProviderId{user:0, app:10230, cmp:ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.widget.EnhancedVideoWidget}}}
08-14 06:30:54.846 16368 16368 I videowidget-widget: === WIDGET UPDATE CALLED ===
08-14 06:30:54.846 16368 16368 D videowidget-widget: Updating widgets: [23]
08-14 06:30:54.847 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:54.847 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:54.849 16368 16463 V videowidget-frames: Frame 2 extracted successfully - Size: 1080x864
08-14 06:30:54.856 16368 16463 V videowidget-frames: Frame 2 resized to: 300x300
08-14 06:30:54.856 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_2.png
08-14 06:30:54.861 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:54.861   604   815 D ShortcutService: changing package: com.enhanced.videowidget userId0
08-14 06:30:54.861 16368 16368 D videowidget-widget: Repository initialized for widget updates
08-14 06:30:54.862 16368 16368 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:54.862 16368 16368 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:54.862 16368 16368 D videowidget-widget: Widget 23 found - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:54.864   604   815 D ShortcutService: handlePackageChanged: com.enhanced.videowidget user=0
08-14 06:30:54.864   604   815 D ShortcutService: rescanPackageIfNeeded <EMAIL>, forceRescan=true , isNewApp=true
08-14 06:30:54.864   604   815 D ShortcutService: Package com.enhanced.videowidget has 0 manifest shortcut(s), and 0 share target(s)
08-14 06:30:54.880 16368 16368 D videowidget-widget: Widget 23 shouldStartProcessing: true
08-14 06:30:54.880 16368 16368 I videowidget-widget: Starting WorkManager for widget 23
08-14 06:30:54.880 16368 16368 D videowidget-widget: === STARTING WORKMANAGER for Widget 23 ===
08-14 06:30:54.880   604   795 D AppWidgetServiceImpl: Trying to notify widget update for package com.enhanced.videowidget with widget id: 23
08-14 06:30:54.884 16368 16368 D videowidget-widget: WorkRequest created for widget 23
08-14 06:30:54.919  1114  1187 D b/267448330: provider: ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.widget.EnhancedVideoWidget}, paddedSizes: [346.18182x344.0, 346.18182x315.63635, 601.8182x200.36363, 601.8182x200.36363], getMinMaxSizes: Rect(346, 200 - 601, 344)
08-14 06:30:54.965 16368 16368 D videowidget-widget: Foreground service started for widget 23
08-14 06:30:54.966 16368 16368 D videowidget-widget: Cancelled existing work for widget 23
08-14 06:30:54.967 16368 16368 D videowidget-widget: WorkRequest enqueued for widget 23
08-14 06:30:55.009 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:55.009 16368 16463 V videowidget-frames: Frame 2 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_2.png
08-14 06:30:55.009 16368 16463 V videowidget-frames: Frame 2/100 - Success: true
08-14 06:30:55.009 16368 16463 V videowidget-frames: Frame 2/100 - Success: true
08-14 06:30:55.009 16368 16463 V videowidget-frames: Extracting frame 3 at timestamp 183000μs (183ms)
08-14 06:30:55.096 16368 16392 I WM-WorkerWrapper: Work [ id=17c472df-fb96-41d0-8f4f-21b33f92a95c, tags={ com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23 } ] was cancelled
08-14 06:30:55.097 16368 16368 D videowidget-widget: WorkManager status for widget 23: 1 work items
08-14 06:30:55.097 16368 16368 D videowidget-widget: Work 02efae81-d6f0-4c9d-b42f-928a286bb623: state=ENQUEUED, tags=[com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23]
08-14 06:30:55.098 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:55.098 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:55.099 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:55.103 16368 16368 D videowidget-service: onStartCommand: com.enhanced.videowidget.ACTION_START_UPDATES
08-14 06:30:55.105   604   691 W ActivityManager: Starting FGS with type dataSync code=4 callerApp=ProcessRecord{1e409d8 16368:com.enhanced.videowidget/u0a230} targetSDK=33 requiredPermissions=all of the permissions allOf=true [android.permission.FOREGROUND_SERVICE_DATA_SYNC]
08-14 06:30:55.125   604  1978 W JobScheduler: Job didn't exist in JobStore: 7d49af4 #u0a230/0 com.enhanced.videowidget/androidx.work.impl.background.systemjob.SystemJobService
08-14 06:30:55.189 16368 16464 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:55.189 16368 16464 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:55.189 16368 16464 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:55.192 16368 16464 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:55.193 16368 16464 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:55.194 16368 16464 D videowidget-worker: Repository initialized for widget 23
08-14 06:30:55.194 16368 16464 D videowidget-worker: === PROCESSING WIDGET FRAMES for Widget 23 ===
08-14 06:30:55.194 16368 16464 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:55.194 16368 16464 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:55.194 16368 16464 D videowidget-worker: Widget found - Video: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4, Total frames: 0, Frames dir: null
08-14 06:30:55.194 16368 16464 D videowidget-worker: Widget state - isPlaying: true, isPaused: false, mediaType: video
08-14 06:30:55.196 16368 16464 D videowidget-worker: shouldStartProcessing: true, shouldAnimate: false
08-14 06:30:55.197 16368 16464 D videowidget-worker: Widget 23 needs frame extraction
08-14 06:30:55.197 16368 16464 I videowidget-worker: === EXTRACTING VIDEO FRAMES for Widget 23 ===
08-14 06:30:55.197 16368 16464 D videowidget-worker: Video path: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:55.201 16368 16465 I videowidget-frames: === STARTING FRAME EXTRACTION ===
08-14 06:30:55.202 16368 16465 D videowidget-frames: Video path: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:55.202 16368 16465 D videowidget-frames: Output directory: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23
08-14 06:30:55.202 16368 16465 D videowidget-frames: Max frames: 100, Target size: 300x300
08-14 06:30:55.203 16368 16465 D videowidget-frames: Setting data source...
08-14 06:30:55.206 16368 16463 V videowidget-frames: Frame 3 extracted successfully - Size: 1080x864
08-14 06:30:55.208 16368 16463 V videowidget-frames: Frame 3 resized to: 300x300
08-14 06:30:55.209 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_3.png
08-14 06:30:55.243 16368 16465 D videowidget-frames: Data source set successfully
08-14 06:30:55.245 16368 16465 D videowidget-frames: Raw duration string: '6100'
08-14 06:30:55.245 16368 16465 D videowidget-frames: Parsed duration: 6100ms
08-14 06:30:55.247 16368 16465 I videowidget-frames: Video duration: 6100ms (6s)
08-14 06:30:55.247 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:55.247 16368 16463 V videowidget-frames: Frame 3 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_3.png
08-14 06:30:55.248 16368 16463 V videowidget-frames: Frame 3/100 - Success: true
08-14 06:30:55.250 16368 16465 I videowidget-frames: Frame interval: 61ms
08-14 06:30:55.252 16368 16465 I videowidget-frames: Will extract 100 frames
08-14 06:30:55.252 16368 16465 D videowidget-frames: Output directory: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23
08-14 06:30:55.252 16368 16463 V videowidget-frames: Frame 3/100 - Success: true
08-14 06:30:55.255 16368 16463 V videowidget-frames: Extracting frame 4 at timestamp 244000μs (244ms)
08-14 06:30:55.256 16368 16465 D videowidget-frames: Output directory already exists
08-14 06:30:55.256 16368 16465 I videowidget-frames: Starting frame extraction loop...
08-14 06:30:55.256 16368 16465 V videowidget-frames: Extracting frame 0 at timestamp 0μs (0ms)
08-14 06:30:55.380 16368 16463 V videowidget-frames: Frame 4 extracted successfully - Size: 1080x864
08-14 06:30:55.381 16368 16463 V videowidget-frames: Frame 4 resized to: 300x300
08-14 06:30:55.382 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_4.png
08-14 06:30:55.463 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:55.463 16368 16463 V videowidget-frames: Frame 4 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_4.png
08-14 06:30:55.464 16368 16463 V videowidget-frames: Frame 4/100 - Success: true
08-14 06:30:55.464 16368 16463 V videowidget-frames: Frame 4/100 - Success: true
08-14 06:30:55.465 16368 16463 V videowidget-frames: Extracting frame 5 at timestamp 305000μs (305ms)
08-14 06:30:55.485 16368 16465 V videowidget-frames: Frame 0 extracted successfully - Size: 1080x864
08-14 06:30:55.490 16368 16465 V videowidget-frames: Frame 0 resized to: 300x300
08-14 06:30:55.491 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_0.png
08-14 06:30:55.535 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:55.535 16368 16465 V videowidget-frames: Frame 0 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_0.png
08-14 06:30:55.536 16368 16465 V videowidget-frames: Frame 0/100 - Success: true
08-14 06:30:55.537 16368 16465 V videowidget-frames: Frame 0/100 - Success: true
08-14 06:30:55.538 16368 16465 V videowidget-frames: Extracting frame 1 at timestamp 61000μs (61ms)
08-14 06:30:55.565 16368 16463 V videowidget-frames: Frame 5 extracted successfully - Size: 1080x864
08-14 06:30:55.565 16368 16463 V videowidget-frames: Frame 5 resized to: 300x300
08-14 06:30:55.566 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_5.png
08-14 06:30:55.596 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:55.596 16368 16463 V videowidget-frames: Frame 5 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_5.png
08-14 06:30:55.597 16368 16463 V videowidget-frames: Frame 5/100 - Success: true
08-14 06:30:55.597 16368 16463 V videowidget-frames: Frame 5/100 - Success: true
08-14 06:30:55.597 16368 16463 V videowidget-frames: Extracting frame 6 at timestamp 366000μs (366ms)
08-14 06:30:55.648 16368 16465 V videowidget-frames: Frame 1 extracted successfully - Size: 1080x864
08-14 06:30:55.649 16368 16465 V videowidget-frames: Frame 1 resized to: 300x300
08-14 06:30:55.650 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_1.png
08-14 06:30:55.679 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:55.680 16368 16465 V videowidget-frames: Frame 1 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_1.png
08-14 06:30:55.681 16368 16465 V videowidget-frames: Frame 1/100 - Success: true
08-14 06:30:55.681 16368 16465 V videowidget-frames: Frame 1/100 - Success: true
08-14 06:30:55.681 16368 16465 V videowidget-frames: Extracting frame 2 at timestamp 122000μs (122ms)
08-14 06:30:55.718 16368 16463 V videowidget-frames: Frame 6 extracted successfully - Size: 1080x864
08-14 06:30:55.719 16368 16463 V videowidget-frames: Frame 6 resized to: 300x300
08-14 06:30:55.719 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_6.png
08-14 06:30:55.748 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:55.748 16368 16463 V videowidget-frames: Frame 6 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_6.png
08-14 06:30:55.748 16368 16463 V videowidget-frames: Frame 6/100 - Success: true
08-14 06:30:55.748 16368 16463 V videowidget-frames: Frame 6/100 - Success: true
08-14 06:30:55.749 16368 16463 V videowidget-frames: Extracting frame 7 at timestamp 427000μs (427ms)
08-14 06:30:55.805 16368 16465 V videowidget-frames: Frame 2 extracted successfully - Size: 1080x864
08-14 06:30:55.807 16368 16465 V videowidget-frames: Frame 2 resized to: 300x300
08-14 06:30:55.807 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_2.png
08-14 06:30:55.837 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:55.837 16368 16465 V videowidget-frames: Frame 2 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_2.png
08-14 06:30:55.838 16368 16465 V videowidget-frames: Frame 2/100 - Success: true
08-14 06:30:55.838 16368 16465 V videowidget-frames: Frame 2/100 - Success: true
08-14 06:30:55.839 16368 16465 V videowidget-frames: Extracting frame 3 at timestamp 183000μs (183ms)
08-14 06:30:55.888 16368 16463 V videowidget-frames: Frame 7 extracted successfully - Size: 1080x864
08-14 06:30:55.889 16368 16463 V videowidget-frames: Frame 7 resized to: 300x300
08-14 06:30:55.889 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_7.png
08-14 06:30:55.925 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:55.926 16368 16463 V videowidget-frames: Frame 7 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_7.png
08-14 06:30:55.927 16368 16463 V videowidget-frames: Frame 7/100 - Success: true
08-14 06:30:55.927 16368 16463 V videowidget-frames: Frame 7/100 - Success: true
08-14 06:30:55.927 16368 16463 V videowidget-frames: Extracting frame 8 at timestamp 488000μs (488ms)
08-14 06:30:55.974 16368 16465 V videowidget-frames: Frame 3 extracted successfully - Size: 1080x864
08-14 06:30:55.977 16368 16465 V videowidget-frames: Frame 3 resized to: 300x300
08-14 06:30:55.977 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_3.png
08-14 06:30:56.013 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:56.014 16368 16465 V videowidget-frames: Frame 3 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_3.png
08-14 06:30:56.015 16368 16465 V videowidget-frames: Frame 3/100 - Success: true
08-14 06:30:56.016 16368 16465 V videowidget-frames: Frame 3/100 - Success: true
08-14 06:30:56.017 16368 16465 V videowidget-frames: Extracting frame 4 at timestamp 244000μs (244ms)
08-14 06:30:56.028  1114  1188 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.enhanced.videowidget]
08-14 06:30:56.034  1320  1320 I AiAiEcho: AppFetcherImpl onPackageChanged com.enhanced.videowidget.
08-14 06:30:56.040  1320 16295 I AiAiEcho: AppIndexer Package:[com.enhanced.videowidget] UserProfile:[0] Enabled:[true].
08-14 06:30:56.040  1320 16295 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.enhanced.videowidget], userId:[0], reason:[package is updated.].
08-14 06:30:56.053   604   815 D ShortcutService: changing package: com.enhanced.videowidget userId0
08-14 06:30:56.054   604   815 D ShortcutService: handlePackageChanged: com.enhanced.videowidget user=0
08-14 06:30:56.054   604   815 D ShortcutService: rescanPackageIfNeeded <EMAIL>, forceRescan=true , isNewApp=true
08-14 06:30:56.056   604   815 D ShortcutService: Package com.enhanced.videowidget has 0 manifest shortcut(s), and 0 share target(s)
08-14 06:30:56.058   604   795 I AppWidgetServiceImpl: cancelBroadcastsLocked() for Provider{ProviderId{user:0, app:10230, cmp:ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.widget.EnhancedVideoWidget}}}
08-14 06:30:56.068 16368 16368 I videowidget-widget: === WIDGET UPDATE CALLED ===
08-14 06:30:56.069 16368 16368 D videowidget-widget: Updating widgets: [23]
08-14 06:30:56.069 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:56.069 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:56.070 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:56.072 16368 16368 D videowidget-widget: Repository initialized for widget updates
08-14 06:30:56.072 16368 16368 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:56.074 16368 16368 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:56.075 16368 16368 D videowidget-widget: Widget 23 found - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:56.097 16368 16368 D videowidget-widget: Widget 23 shouldStartProcessing: true
08-14 06:30:56.097 16368 16368 I videowidget-widget: Starting WorkManager for widget 23
08-14 06:30:56.097   604   795 D AppWidgetServiceImpl: Trying to notify widget update for package com.enhanced.videowidget with widget id: 23
08-14 06:30:56.098 16368 16368 D videowidget-widget: === STARTING WORKMANAGER for Widget 23 ===
08-14 06:30:56.099 16368 16368 D videowidget-widget: WorkRequest created for widget 23
08-14 06:30:56.123 16368 16368 D videowidget-widget: Foreground service started for widget 23
08-14 06:30:56.123 16368 16368 D videowidget-widget: Cancelled existing work for widget 23
08-14 06:30:56.130 16368 16368 D videowidget-widget: WorkRequest enqueued for widget 23
08-14 06:30:56.158 16368 16463 V videowidget-frames: Frame 8 extracted successfully - Size: 1080x864
08-14 06:30:56.173 16368 16463 V videowidget-frames: Frame 8 resized to: 300x300
08-14 06:30:56.173 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_8.png
08-14 06:30:56.206 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:56.207 16368 16463 V videowidget-frames: Frame 8 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_8.png
08-14 06:30:56.207 16368 16463 V videowidget-frames: Frame 8/100 - Success: true
08-14 06:30:56.207 16368 16463 V videowidget-frames: Frame 8/100 - Success: true
08-14 06:30:56.207 16368 16463 V videowidget-frames: Extracting frame 9 at timestamp 549000μs (549ms)
08-14 06:30:56.306 16368 16392 I WM-WorkerWrapper: Work [ id=02efae81-d6f0-4c9d-b42f-928a286bb623, tags={ com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23 } ] was cancelled
08-14 06:30:56.306 16368 16368 D videowidget-widget: WorkManager status for widget 23: 1 work items
08-14 06:30:56.307 16368 16368 D videowidget-widget: Work 147499d1-8f35-45f8-a346-e1f7decacc7c: state=ENQUEUED, tags=[com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23]
08-14 06:30:56.308 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:56.309 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:56.311 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:56.312 16368 16368 D videowidget-service: onStartCommand: com.enhanced.videowidget.ACTION_START_UPDATES
08-14 06:30:56.315   604   619 W ActivityManager: Starting FGS with type dataSync code=4 callerApp=ProcessRecord{1e409d8 16368:com.enhanced.videowidget/u0a230} targetSDK=33 requiredPermissions=all of the permissions allOf=true [android.permission.FOREGROUND_SERVICE_DATA_SYNC]
08-14 06:30:56.335   604   619 W JobScheduler: Job didn't exist in JobStore: 8d8addc #u0a230/1 com.enhanced.videowidget/androidx.work.impl.background.systemjob.SystemJobService
08-14 06:30:56.362 16368 16515 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:56.362 16368 16515 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:56.363 16368 16515 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:56.363 16368 16515 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:56.379 16368 16515 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:56.379 16368 16515 D videowidget-worker: Repository initialized for widget 23
08-14 06:30:56.379 16368 16515 D videowidget-worker: === PROCESSING WIDGET FRAMES for Widget 23 ===
08-14 06:30:56.380 16368 16515 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:56.380 16368 16515 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:56.380 16368 16515 D videowidget-worker: Widget found - Video: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4, Total frames: 0, Frames dir: null
08-14 06:30:56.383 16368 16515 D videowidget-worker: Widget state - isPlaying: true, isPaused: false, mediaType: video
08-14 06:30:56.383 16368 16515 D videowidget-worker: shouldStartProcessing: true, shouldAnimate: false
08-14 06:30:56.383 16368 16515 D videowidget-worker: Widget 23 needs frame extraction
08-14 06:30:56.384 16368 16515 I videowidget-worker: === EXTRACTING VIDEO FRAMES for Widget 23 ===
08-14 06:30:56.384 16368 16515 D videowidget-worker: Video path: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:56.388 16368 16464 I videowidget-frames: === STARTING FRAME EXTRACTION ===
08-14 06:30:56.388 16368 16464 D videowidget-frames: Video path: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:56.388 16368 16464 D videowidget-frames: Output directory: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23
08-14 06:30:56.389 16368 16464 D videowidget-frames: Max frames: 100, Target size: 300x300
08-14 06:30:56.390 16368 16464 D videowidget-frames: Setting data source...
08-14 06:30:56.426 16368 16465 V videowidget-frames: Frame 4 extracted successfully - Size: 1080x864
08-14 06:30:56.427 16368 16465 V videowidget-frames: Frame 4 resized to: 300x300
08-14 06:30:56.428 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_4.png
08-14 06:30:56.469 16368 16464 D videowidget-frames: Data source set successfully
08-14 06:30:56.476 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:56.478 16368 16465 V videowidget-frames: Frame 4 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_4.png
08-14 06:30:56.478 16368 16465 V videowidget-frames: Frame 4/100 - Success: true
08-14 06:30:56.478 16368 16465 V videowidget-frames: Frame 4/100 - Success: true
08-14 06:30:56.480 16368 16464 D videowidget-frames: Raw duration string: '6100'
08-14 06:30:56.480 16368 16464 D videowidget-frames: Parsed duration: 6100ms
08-14 06:30:56.480 16368 16464 I videowidget-frames: Video duration: 6100ms (6s)
08-14 06:30:56.480 16368 16464 I videowidget-frames: Frame interval: 61ms
08-14 06:30:56.480 16368 16464 I videowidget-frames: Will extract 100 frames
08-14 06:30:56.480 16368 16464 D videowidget-frames: Output directory: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23
08-14 06:30:56.481 16368 16464 D videowidget-frames: Output directory already exists
08-14 06:30:56.481 16368 16464 I videowidget-frames: Starting frame extraction loop...
08-14 06:30:56.483 16368 16465 V videowidget-frames: Extracting frame 5 at timestamp 305000μs (305ms)
08-14 06:30:56.484 16368 16464 V videowidget-frames: Extracting frame 0 at timestamp 0μs (0ms)
08-14 06:30:56.499   604  1978 W NotificationService: Toast already killed. pkg=com.enhanced.videowidget token=android.os.BinderProxy@decd4e3
08-14 06:30:56.550 16368 16463 V videowidget-frames: Frame 9 extracted successfully - Size: 1080x864
08-14 06:30:56.551 16368 16463 V videowidget-frames: Frame 9 resized to: 300x300
08-14 06:30:56.551 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_9.png
08-14 06:30:56.584 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:56.584 16368 16463 V videowidget-frames: Frame 9 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_9.png
08-14 06:30:56.584 16368 16463 V videowidget-frames: Frame 9/100 - Success: true
08-14 06:30:56.584 16368 16463 V videowidget-frames: Frame 9/100 - Success: true
08-14 06:30:56.585 16368 16463 V videowidget-frames: Extracting frame 10 at timestamp 610000μs (610ms)
08-14 06:30:56.675 16368 16465 V videowidget-frames: Frame 5 extracted successfully - Size: 1080x864
08-14 06:30:56.676 16368 16465 V videowidget-frames: Frame 5 resized to: 300x300
08-14 06:30:56.677 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_5.png
08-14 06:30:56.729 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:56.729 16368 16465 V videowidget-frames: Frame 5 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_5.png
08-14 06:30:56.730 16368 16465 V videowidget-frames: Frame 5/100 - Success: true
08-14 06:30:56.730 16368 16465 V videowidget-frames: Frame 5/100 - Success: true
08-14 06:30:56.730 16368 16465 V videowidget-frames: Extracting frame 6 at timestamp 366000μs (366ms)
08-14 06:30:56.767 16368 16464 V videowidget-frames: Frame 0 extracted successfully - Size: 1080x864
08-14 06:30:56.768 16368 16464 V videowidget-frames: Frame 0 resized to: 300x300
08-14 06:30:56.769 16368 16464 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_0.png
08-14 06:30:56.805 16368 16464 V videowidget-frames: Frame compression result: true
08-14 06:30:56.805 16368 16464 V videowidget-frames: Frame 0 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_0.png
08-14 06:30:56.805 16368 16464 V videowidget-frames: Frame 0/100 - Success: true
08-14 06:30:56.805 16368 16464 V videowidget-frames: Frame 0/100 - Success: true
08-14 06:30:56.807 16368 16464 V videowidget-frames: Extracting frame 1 at timestamp 61000μs (61ms)
08-14 06:30:56.849 16368 16463 V videowidget-frames: Frame 10 extracted successfully - Size: 1080x864
08-14 06:30:56.851 16368 16463 V videowidget-frames: Frame 10 resized to: 300x300
08-14 06:30:56.851 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_10.png
08-14 06:30:56.887 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:56.887 16368 16463 V videowidget-frames: Frame 10 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_10.png
08-14 06:30:56.888 16368 16463 V videowidget-frames: Frame 10/100 - Success: true
08-14 06:30:56.888 16368 16463 V videowidget-frames: Frame 10/100 - Success: true
08-14 06:30:56.889 16368 16463 V videowidget-frames: Extracting frame 11 at timestamp 671000μs (671ms)
08-14 06:30:56.952 16368 16465 V videowidget-frames: Frame 6 extracted successfully - Size: 1080x864
08-14 06:30:56.953 16368 16465 V videowidget-frames: Frame 6 resized to: 300x300
08-14 06:30:56.953 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_6.png
08-14 06:30:56.983 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:56.983 16368 16465 V videowidget-frames: Frame 6 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_6.png
08-14 06:30:56.984 16368 16465 V videowidget-frames: Frame 6/100 - Success: true
08-14 06:30:56.984 16368 16465 V videowidget-frames: Frame 6/100 - Success: true
08-14 06:30:56.985 16368 16465 V videowidget-frames: Extracting frame 7 at timestamp 427000μs (427ms)
08-14 06:30:57.033 16368 16464 V videowidget-frames: Frame 1 extracted successfully - Size: 1080x864
08-14 06:30:57.036 16368 16464 V videowidget-frames: Frame 1 resized to: 300x300
08-14 06:30:57.037 16368 16464 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_1.png
08-14 06:30:57.090 16368 16464 V videowidget-frames: Frame compression result: true
08-14 06:30:57.091 16368 16464 V videowidget-frames: Frame 1 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_1.png
08-14 06:30:57.092 16368 16464 V videowidget-frames: Frame 1/100 - Success: true
08-14 06:30:57.092 16368 16464 V videowidget-frames: Frame 1/100 - Success: true
08-14 06:30:57.092 16368 16464 V videowidget-frames: Extracting frame 2 at timestamp 122000μs (122ms)
08-14 06:30:57.117 16368 16463 V videowidget-frames: Frame 11 extracted successfully - Size: 1080x864
08-14 06:30:57.119 16368 16463 V videowidget-frames: Frame 11 resized to: 300x300
08-14 06:30:57.119 16368 16463 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_11.png
08-14 06:30:57.169 16368 16463 V videowidget-frames: Frame compression result: true
08-14 06:30:57.169 16368 16463 V videowidget-frames: Frame 11 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_11.png
08-14 06:30:57.169 16368 16463 V videowidget-frames: Frame 11/100 - Success: true
08-14 06:30:57.169 16368 16463 V videowidget-frames: Frame 11/100 - Success: true
08-14 06:30:57.170 16368 16463 V videowidget-frames: Extracting frame 12 at timestamp 732000μs (732ms)
08-14 06:30:57.172   604   815 D ShortcutService: changing package: com.enhanced.videowidget userId0
08-14 06:30:57.172   604   815 D ShortcutService: handlePackageChanged: com.enhanced.videowidget user=0
08-14 06:30:57.172   604   815 D ShortcutService: rescanPackageIfNeeded <EMAIL>, forceRescan=true , isNewApp=true
08-14 06:30:57.173   604   815 D ShortcutService: Package com.enhanced.videowidget has 0 manifest shortcut(s), and 0 share target(s)
08-14 06:30:57.177   604   795 I AppWidgetServiceImpl: cancelBroadcastsLocked() for Provider{ProviderId{user:0, app:10230, cmp:ComponentInfo{com.enhanced.videowidget/com.enhanced.videowidget.widget.EnhancedVideoWidget}}}
08-14 06:30:57.189 16368 16368 I videowidget-widget: === WIDGET UPDATE CALLED ===
08-14 06:30:57.189 16368 16368 D videowidget-widget: Updating widgets: [23]
08-14 06:30:57.189 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:57.190 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:57.190 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:57.190 16368 16368 D videowidget-widget: Repository initialized for widget updates
08-14 06:30:57.190 16368 16368 D videowidget-repo: getWidget(23) - Found: true, Total widgets: 1
08-14 06:30:57.190 16368 16368 D videowidget-repo: Widget 23 details - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:57.190 16368 16368 D videowidget-widget: Widget 23 found - isPlaying: true, isPaused: false, totalFrames: 0, videoPath: /data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4
08-14 06:30:57.199  1320  1320 I AiAiEcho: AppFetcherImpl onPackageChanged com.enhanced.videowidget.
08-14 06:30:57.203  1320 16507 I AiAiEcho: AppIndexer Package:[com.enhanced.videowidget] UserProfile:[0] Enabled:[true].
08-14 06:30:57.204  1320 16507 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.enhanced.videowidget], userId:[0], reason:[package is updated.].
08-14 06:30:57.205 16368 16368 D videowidget-widget: Widget 23 shouldStartProcessing: true
08-14 06:30:57.205 16368 16368 I videowidget-widget: Starting WorkManager for widget 23
08-14 06:30:57.205 16368 16368 D videowidget-widget: === STARTING WORKMANAGER for Widget 23 ===
08-14 06:30:57.205 16368 16368 D videowidget-widget: WorkRequest created for widget 23
08-14 06:30:57.212   604   795 D AppWidgetServiceImpl: Trying to notify widget update for package com.enhanced.videowidget with widget id: 23
08-14 06:30:57.238  1114  1188 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.enhanced.videowidget]
08-14 06:30:57.246 16368 16368 D videowidget-widget: Foreground service started for widget 23
08-14 06:30:57.246 16368 16368 D videowidget-widget: Cancelled existing work for widget 23
08-14 06:30:57.250 16368 16368 D videowidget-widget: WorkRequest enqueued for widget 23
08-14 06:30:57.272 16368 16465 V videowidget-frames: Frame 7 extracted successfully - Size: 1080x864
08-14 06:30:57.278 16368 16465 V videowidget-frames: Frame 7 resized to: 300x300
08-14 06:30:57.278 16368 16465 V videowidget-frames: Saving frame to: /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_7.png
08-14 06:30:57.317 16368 16465 V videowidget-frames: Frame compression result: true
08-14 06:30:57.319 16368 16465 V videowidget-frames: Frame 7 saved to file: true - /data/user/0/com.enhanced.videowidget/files/widget_frames/widget_23/frame_7.png
08-14 06:30:57.319 16368 16465 V videowidget-frames: Frame 7/100 - Success: true
08-14 06:30:57.319 16368 16465 V videowidget-frames: Frame 7/100 - Success: true
08-14 06:30:57.320 16368 16465 V videowidget-frames: Extracting frame 8 at timestamp 488000μs (488ms)
08-14 06:30:57.362 16368 16368 D videowidget-widget: WorkManager status for widget 23: 1 work items
08-14 06:30:57.363 16368 16368 D videowidget-widget: Work 96943855-49d7-4f2b-aada-5d0f258ee128: state=ENQUEUED, tags=[com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23]
08-14 06:30:57.363 16368 16368 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:57.364 16368 16368 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:57.364 16368 16368 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:57.364 16368 16368 D videowidget-service: onStartCommand: com.enhanced.videowidget.ACTION_START_UPDATES
08-14 06:30:57.366 16368 16392 I WM-WorkerWrapper: Work [ id=147499d1-8f35-45f8-a346-e1f7decacc7c, tags={ com.enhanced.videowidget.worker.WidgetUpdateWorker, widget_update, widget_23 } ] was cancelled
08-14 06:30:57.367   604   934 W ActivityManager: Starting FGS with type dataSync code=4 callerApp=ProcessRecord{1e409d8 16368:com.enhanced.videowidget/u0a230} targetSDK=33 requiredPermissions=all of the permissions allOf=true [android.permission.FOREGROUND_SERVICE_DATA_SYNC]
08-14 06:30:57.387   604  1978 W JobScheduler: Job didn't exist in JobStore: 45573f1 #u0a230/2 com.enhanced.videowidget/androidx.work.impl.background.systemjob.SystemJobService
08-14 06:30:57.396 16368 16515 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:57.397 16368 16515 I videowidget-worker: === WORKER STARTED for Widget 23 ===
08-14 06:30:57.397 16368 16515 D videowidget-repo: Loading widgets from storage - File exists: true, Path: /data/user/0/com.enhanced.videowidget/files/enhanced_widgets.json
08-14 06:30:57.399 16368 16515 D videowidget-repo: Widget JSON content: [{"createdAt":1755133253598,"currentFrameIndex":0,"description":"Duration: 6s","framerate":30.0,"id":23,"isPaused":false,"isPlaying":true,"lastUpdated":1755133253598,"mediaType":"video","name":"Assa","totalFrames":0,"videoPath":"/data/user/0/com.enhanced.videowidget/files/videos/e22cbf43-4755-438d-9e4c-ee021600ab41.mp4"}]
08-14 06:30:57.400 16368 16515 D videowidget-repo: Loaded 1 widgets from storage
08-14 06:30:57.402 16368 16515 D videowidget-worker: Repository initialized for widget 23