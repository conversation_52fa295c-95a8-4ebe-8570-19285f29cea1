<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.VideoWidgetManagerActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Widget Preview Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Widget Name -->
                <TextView
                    android:id="@+id/textWidgetName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textStyle="bold"
                    tools:text="My Video Widget" />

                <!-- Widget Description -->
                <TextView
                    android:id="@+id/textWidgetDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?android:attr/textColorSecondary"
                    tools:text="Duration: 30s" />

                <!-- Preview Image -->
                <ImageView
                    android:id="@+id/imagePreview"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rounded_corner_background"
                    android:contentDescription="Widget preview"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_video_placeholder" />

                <!-- Control Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/buttonPlayPause"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:drawablePadding="8dp"
                        android:text="Play"
                        app:icon="@drawable/ic_play_arrow" />

                    <Button
                        android:id="@+id/buttonUpdate"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:drawablePadding="8dp"
                        android:text="Update"
                        app:icon="@drawable/ic_edit" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Widget Details Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Widget Details"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textWidgetDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="monospace"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?android:attr/textColorSecondary"
                    tools:text="Widget ID: 1\nMedia Type: video\nTotal Frames: 100\nFrame Rate: 30.0 fps\nCurrent Frame: 1/100\nCreated: Jan 01, 2024 12:00\nLast Updated: Jan 01, 2024 12:00" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Widget Settings Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Widget Settings"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textStyle="bold" />

                <!-- Pause Frame Display Setting -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Show Frame When Paused"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Display current video frame when widget is paused instead of blank widget"
                            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                            android:textColor="?android:attr/textColorSecondary" />

                    </LinearLayout>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchShowPausedFrame"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:checked="true" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Danger Zone Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/error_color"
            app:strokeWidth="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Danger Zone"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="@color/error_color"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="Deleting a widget will permanently remove it from your home screen and delete all associated data."
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?android:attr/textColorSecondary" />

                <Button
                    android:id="@+id/buttonDelete"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:drawablePadding="8dp"
                    android:text="Delete Widget"
                    android:textColor="@color/error_color"
                    app:icon="@drawable/ic_delete"
                    app:iconTint="@color/error_color"
                    app:strokeColor="@color/error_color" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
