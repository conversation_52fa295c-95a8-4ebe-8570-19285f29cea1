package com.enhanced.videowidget.worker

import android.appwidget.AppWidgetManager
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.utils.VideoFrameExtractor
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.coroutines.coroutineContext
import com.enhanced.videowidget.utils.AppLogger
import java.io.File

/**
 * Background worker for processing video frames and updating widgets
 */
class WidgetUpdateWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val DEFAULT_FRAME_DELAY = 33L // ~30 FPS
    }
    
    private val repository = WidgetRepository.getInstance()
    private val appWidgetManager = AppWidgetManager.getInstance(applicationContext)
    
    override suspend fun doWork(): Result {
        // Set context for toast debugging
        AppLogger.setContext(applicationContext)

        val widgetId = inputData.getInt(EnhancedVideoWidget.EXTRA_WIDGET_ID, -1)
        if (widgetId == -1) {
            AppLogger.e(AppLogger.TAG_WORKER, "Invalid widget ID received")
            return Result.failure()
        }

        AppLogger.logWorkerStart(widgetId)

        repository.initialize(applicationContext)
        AppLogger.d(AppLogger.TAG_WORKER, "Repository initialized for widget $widgetId")

        return try {
            processWidgetFrames(widgetId)
            AppLogger.logWorkerEnd(widgetId, true)
            Result.success()
        } catch (e: Exception) {
            AppLogger.logError(AppLogger.TAG_WORKER, "processWidgetFrames for widget $widgetId", e)
            AppLogger.logWorkerEnd(widgetId, false)
            Result.failure()
        }
    }
    
    private suspend fun processWidgetFrames(widgetId: Int) {
        AppLogger.d(AppLogger.TAG_WORKER, "=== PROCESSING WIDGET FRAMES for Widget $widgetId ===")

        var widget = repository.getWidget(widgetId)
        if (widget == null) {
            AppLogger.w(AppLogger.TAG_WORKER, "Widget $widgetId not found in repository")
            return
        }

        AppLogger.d(AppLogger.TAG_WORKER, "Widget found - Video: ${widget.videoPath}, Total frames: ${widget.totalFrames}, Frames dir: ${widget.framesDirectory}")
        AppLogger.d(AppLogger.TAG_WORKER, "Widget state - isPlaying: ${widget.isPlaying}, isPaused: ${widget.isPaused}, mediaType: ${widget.mediaType}")
        AppLogger.d(AppLogger.TAG_WORKER, "shouldStartProcessing: ${widget.shouldStartProcessing()}, shouldAnimate: ${widget.shouldAnimate()}")

        // If widget should not start processing, don't process frames
        if (!widget.shouldStartProcessing()) {
            AppLogger.d(AppLogger.TAG_WORKER, "Widget $widgetId should not start processing, stopping work")
            return
        }
        
        // Check if we need to extract frames first
        if (widget.totalFrames == 0 && !widget.videoPath.isNullOrEmpty()) {
            AppLogger.d(AppLogger.TAG_WORKER, "Widget $widgetId needs frame extraction")

            // Check if frames directory already exists with frames
            val framesDir = File(applicationContext.filesDir, "widget_frames/widget_${widget.id}")
            val existingFrames = if (framesDir.exists()) {
                framesDir.listFiles { _, name -> name.startsWith("frame_") && name.endsWith(".png") }?.size ?: 0
            } else {
                0
            }

            if (existingFrames > 0) {
                AppLogger.d(AppLogger.TAG_WORKER, "Found $existingFrames existing frames for widget $widgetId, updating repository")
                val updatedWidget = widget.copy(
                    totalFrames = existingFrames,
                    framesDirectory = framesDir.absolutePath
                )
                repository.addOrUpdateWidget(updatedWidget)
                widget = updatedWidget
            } else {
                AppLogger.d(AppLogger.TAG_WORKER, "No existing frames found, extracting frames for widget $widgetId")
                extractVideoFrames(widget)

                // Give the repository time to update
                delay(100)

                widget = repository.getWidget(widgetId) ?: return
                AppLogger.d(AppLogger.TAG_WORKER, "After extraction - Total frames: ${widget.totalFrames}")
            }
        }

        // Start frame animation loop only if widget should animate
        if (widget.shouldAnimate()) {
            AppLogger.d(AppLogger.TAG_WORKER, "Starting animation for widget $widgetId")

            // Verify first frame is available before starting animation
            val firstFrame = repository.loadFrameBitmap(widget.id, 0)
            if (firstFrame != null) {
                AppLogger.d(AppLogger.TAG_WORKER, "First frame verified, starting animation")
                animateFrames(widget)
            } else {
                AppLogger.w(AppLogger.TAG_WORKER, "First frame not available, cannot start animation")
            }
        } else {
            AppLogger.d(AppLogger.TAG_WORKER, "Widget $widgetId should not animate - totalFrames: ${widget.totalFrames}, shouldAnimate: ${widget.shouldAnimate()}")
            AppLogger.d(AppLogger.TAG_WORKER, "Widget state: isPlaying=${widget.isPlaying}, isPaused=${widget.isPaused}, mediaType=${widget.mediaType}")
        }
    }
    
    private suspend fun extractVideoFrames(widget: Widget) {
        AppLogger.i(AppLogger.TAG_WORKER, "=== EXTRACTING VIDEO FRAMES for Widget ${widget.id} ===")

        if (widget.videoPath.isNullOrEmpty()) {
            AppLogger.e(AppLogger.TAG_WORKER, "No video path specified for widget ${widget.id}")
            return
        }

        AppLogger.d(AppLogger.TAG_WORKER, "Video path: ${widget.videoPath}")
        
        val frameExtractor = VideoFrameExtractor()
        val frameDirectory = repository.getFrameDirectory(widget.id) ?: return
        
        try {
            val extractedFrames = frameExtractor.extractFrames(
                videoPath = widget.videoPath,
                outputDirectory = frameDirectory,
                maxFrames = 100, // Limit frames to prevent excessive storage usage
                targetWidth = 300,
                targetHeight = 300
            )
            
            if (extractedFrames.isNotEmpty()) {
                // Load first frame as preview
                val firstFrame = repository.loadFrameBitmap(widget.id, 0)
                
                // Update widget with frame information
                val updatedWidget = widget.copy(
                    totalFrames = extractedFrames.size,
                    firstFrame = firstFrame,
                    framesDirectory = frameDirectory
                )
                
                repository.addOrUpdateWidget(updatedWidget)
                
                // Update widget UI with first frame
                EnhancedVideoWidget.updateAppWidget(
                    applicationContext,
                    appWidgetManager,
                    widget.id,
                    updatedWidget,
                    firstFrame
                )
                
                AppLogger.i(AppLogger.TAG_WORKER, "Successfully extracted ${extractedFrames.size} frames for widget ${widget.id}")
            } else {
                AppLogger.w(AppLogger.TAG_WORKER, "No frames extracted for widget ${widget.id}")
            }
        } catch (e: Exception) {
            AppLogger.logError(AppLogger.TAG_WORKER, "extracting frames for widget ${widget.id}", e)
        }
    }
    
    private suspend fun animateFrames(initialWidget: Widget) = withContext(Dispatchers.Default) {
        var widget = initialWidget
        val frameDelay = calculateFrameDelay(widget.framerate)

        AppLogger.i(AppLogger.TAG_WORKER, "=== STARTING ANIMATION for Widget ${widget.id} with ${widget.totalFrames} frames ===")
        AppLogger.d(AppLogger.TAG_WORKER, "Coroutine context active: ${coroutineContext.isActive}")
        AppLogger.d(AppLogger.TAG_WORKER, "Frame delay: ${frameDelay}ms")

        var frameCount = 0
        val maxFrames = 1000 // Prevent infinite loops during testing
        while (coroutineContext.isActive && frameCount < maxFrames) {
            frameCount++
            AppLogger.v(AppLogger.TAG_WORKER, "Animation loop iteration $frameCount for widget ${widget.id}")

            if (!widget.shouldAnimate()) {
                AppLogger.d(AppLogger.TAG_WORKER, "Widget ${widget.id} stopped animating - isPlaying: ${widget.isPlaying}, isPaused: ${widget.isPaused}, totalFrames: ${widget.totalFrames}")

                // If paused, wait longer and check again instead of breaking immediately
                if (widget.isPaused && widget.isPlaying) {
                    AppLogger.d(AppLogger.TAG_WORKER, "Widget ${widget.id} is paused, waiting...")
                    delay(5000) // Wait 5 seconds before checking again
                    continue
                } else {
                    // Widget is stopped, exit the loop
                    break
                }
            }

            AppLogger.v(AppLogger.TAG_WORKER, "Animating widget ${widget.id} - frame ${widget.currentFrameIndex}/${widget.totalFrames}")
            
            // Load current frame (async)
            val frameBitmap = repository.loadFrameBitmap(widget.id, widget.currentFrameIndex)

            if (frameBitmap != null) {
                // Update widget UI with current frame
                EnhancedVideoWidget.updateAppWidget(
                    applicationContext,
                    appWidgetManager,
                    widget.id,
                    widget,
                    frameBitmap
                )

                // Move to next frame (async)
                val nextFrameIndex = widget.getNextFrameIndex()
                repository.updateWidgetFrameIndex(widget.id, nextFrameIndex)

                // Get updated widget from repository to ensure consistency
                val updatedWidget = repository.getWidget(widget.id)
                if (updatedWidget == null) {
                    AppLogger.w(AppLogger.TAG_WORKER, "Widget ${widget.id} no longer exists after frame update")
                    break
                }
                widget = updatedWidget

                AppLogger.logWidgetUpdate(widget.id, widget.currentFrameIndex, widget.isPlaying)
            } else {
                AppLogger.w(AppLogger.TAG_WORKER, "Failed to load frame ${widget.currentFrameIndex} for widget ${widget.id}")

                // If we can't load the frame, try to re-extract frames
                if (!widget.videoPath.isNullOrEmpty()) {
                    extractVideoFrames(widget)
                    val reloadedWidget = repository.getWidget(widget.id)
                    if (reloadedWidget == null) {
                        break
                    }
                    widget = reloadedWidget
                } else {
                    break
                }
            }
            
            // Wait for next frame
            delay(frameDelay)
        }
        
        AppLogger.i(AppLogger.TAG_WORKER, "=== ANIMATION STOPPED for Widget ${widget.id} ===")
        AppLogger.d(AppLogger.TAG_WORKER, "Animation stopped after $frameCount frames. Coroutine active: ${coroutineContext.isActive}, Max frames reached: ${frameCount >= maxFrames}")
    }
    
    private fun calculateFrameDelay(framerate: Float): Long {
        return if (framerate > 0) {
            (1000 / framerate).toLong()
        } else {
            DEFAULT_FRAME_DELAY
        }
    }
}
