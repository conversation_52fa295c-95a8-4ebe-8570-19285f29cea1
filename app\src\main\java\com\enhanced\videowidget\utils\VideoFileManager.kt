package com.enhanced.videowidget.utils

import android.content.Context
import android.net.Uri
import android.provider.DocumentsContract
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.UUID

/**
 * Utility class for managing video files in the app's private directory
 */
class VideoFileManager(private val context: Context) {

    companion object {
        private const val VIDEOS_DIR = "videos"
        private const val TAG = "VideoFileManager"
    }

    /**
     * Get the videos directory in app's private storage
     */
    private fun getVideosDirectory(): File {
        val videosDir = File(context.filesDir, VIDEOS_DIR)
        if (!videosDir.exists()) {
            videosDir.mkdirs()
        }
        return videosDir
    }

    /**
     * Copy a video from URI to app's private directory
     * @param videoUri The URI of the video to copy
     * @return The path to the copied video file, or null if failed
     */
    suspend fun copyVideoToPrivateDirectory(videoUri: Uri): String? = withContext(Dispatchers.IO) {
        try {
            AppLogger.d(TAG, "Starting to copy video from URI: $videoUri")
            
            // Get the original filename if possible
            val originalFileName = getFileName(videoUri)
            val fileExtension = getFileExtension(originalFileName)
            
            // Generate a unique filename
            val uniqueFileName = "${UUID.randomUUID()}$fileExtension"
            val destinationFile = File(getVideosDirectory(), uniqueFileName)
            
            AppLogger.d(TAG, "Copying to: ${destinationFile.absolutePath}")
            
            // Open input stream from the URI
            val inputStream: InputStream? = context.contentResolver.openInputStream(videoUri)
            if (inputStream == null) {
                AppLogger.e(TAG, "Failed to open input stream for URI: $videoUri")
                return@withContext null
            }
            
            // Copy the file
            inputStream.use { input ->
                FileOutputStream(destinationFile).use { output ->
                    val buffer = ByteArray(8192)
                    var bytesRead: Int
                    var totalBytes = 0L
                    
                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        totalBytes += bytesRead
                    }
                    
                    AppLogger.d(TAG, "Successfully copied $totalBytes bytes to ${destinationFile.absolutePath}")
                }
            }
            
            // Verify the file was created and has content
            if (destinationFile.exists() && destinationFile.length() > 0) {
                AppLogger.i(TAG, "Video successfully copied to: ${destinationFile.absolutePath}")

                // Additional validation - try to read basic video metadata
                try {
                    val testRetriever = android.media.MediaMetadataRetriever()
                    testRetriever.setDataSource(destinationFile.absolutePath)
                    val hasVideo = testRetriever.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_HAS_VIDEO)
                    val mimeType = testRetriever.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_MIMETYPE)
                    testRetriever.release()

                    if (hasVideo == "yes") {
                        AppLogger.i(TAG, "Video file validation successful - MimeType: $mimeType")
                        return@withContext destinationFile.absolutePath
                    } else {
                        AppLogger.e(TAG, "Copied file is not a valid video file")
                        destinationFile.delete()
                        return@withContext null
                    }
                } catch (e: Exception) {
                    AppLogger.e(TAG, "Video validation failed - file may be corrupted", e)
                    destinationFile.delete()
                    return@withContext null
                }
            } else {
                AppLogger.e(TAG, "Copied file is empty or doesn't exist")
                return@withContext null
            }
            
        } catch (e: Exception) {
            AppLogger.e(TAG, "Error copying video", e)
            return@withContext null
        }
    }

    /**
     * Get the filename from a URI
     */
    private fun getFileName(uri: Uri): String {
        var fileName = "video.mp4" // default
        
        try {
            // Try to get filename from content resolver
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(MediaStore.Video.Media.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        val name = cursor.getString(nameIndex)
                        if (!name.isNullOrEmpty()) {
                            fileName = name
                        }
                    }
                }
            }
        } catch (e: Exception) {
            AppLogger.w(TAG, "Could not get filename from URI, using default", e)
        }
        
        return fileName
    }

    /**
     * Get file extension from filename
     */
    private fun getFileExtension(fileName: String): String {
        val lastDot = fileName.lastIndexOf('.')
        return if (lastDot >= 0) {
            fileName.substring(lastDot)
        } else {
            ".mp4" // default extension
        }
    }

    /**
     * Delete a video file from private directory
     */
    fun deleteVideo(videoPath: String): Boolean {
        return try {
            val file = File(videoPath)
            if (file.exists() && file.delete()) {
                AppLogger.d(TAG, "Successfully deleted video: $videoPath")
                true
            } else {
                AppLogger.w(TAG, "Failed to delete video or file doesn't exist: $videoPath")
                false
            }
        } catch (e: Exception) {
            AppLogger.e(TAG, "Error deleting video: $videoPath", e)
            false
        }
    }

    /**
     * Get the size of a video file
     */
    fun getVideoSize(videoPath: String): Long {
        return try {
            File(videoPath).length()
        } catch (e: Exception) {
            AppLogger.e(TAG, "Error getting video size: $videoPath", e)
            0L
        }
    }

    /**
     * Check if a video file exists in private directory
     */
    fun videoExists(videoPath: String): Boolean {
        return try {
            File(videoPath).exists()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Clean up old video files to free space
     */
    suspend fun cleanupOldVideos(maxFiles: Int = 10) = withContext(Dispatchers.IO) {
        try {
            val videosDir = getVideosDirectory()
            val videoFiles = videosDir.listFiles()?.sortedByDescending { it.lastModified() }
            
            if (videoFiles != null && videoFiles.size > maxFiles) {
                val filesToDelete = videoFiles.drop(maxFiles)
                filesToDelete.forEach { file ->
                    if (file.delete()) {
                        AppLogger.d(TAG, "Cleaned up old video: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            AppLogger.e(TAG, "Error during cleanup", e)
        }
    }
}
