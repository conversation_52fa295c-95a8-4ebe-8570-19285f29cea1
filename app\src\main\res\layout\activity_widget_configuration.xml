<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.WidgetConfigurationActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Widget Name Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="Widget Name (Optional)"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextWidgetName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Video Selection Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Select Video"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSelectVideo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/select_video"
                android:drawableStart="@drawable/ic_video"
                android:drawablePadding="8dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <TextView
                android:id="@+id/textViewSelectedVideo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/no_video_selected"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary"
                android:layout_marginBottom="16dp" />

            <!-- Video Preview -->
            <ImageView
                android:id="@+id/imageViewPreview"
                android:layout_width="200dp"
                android:layout_height="150dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:background="@drawable/widget_background"
                android:scaleType="centerCrop"
                android:visibility="gone"
                android:contentDescription="Video preview"
                tools:visibility="visible"
                tools:src="@drawable/placeholder_video" />

            <TextView
                android:id="@+id/textViewVideoInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorTertiary"
                android:gravity="center"
                android:layout_marginBottom="32dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="Duration: 30s, 1920x1080" />

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/cancel"
                    style="@style/Widget.MaterialComponents.Button.TextButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonCreateWidget"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/create_widget"
                    android:enabled="false" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Loading Progress -->
    <LinearLayout
        android:id="@+id/progressContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Widget.AppCompat.ProgressBar" />

        <TextView
            android:id="@+id/progressText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Processing..."
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary"
            android:gravity="center" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
