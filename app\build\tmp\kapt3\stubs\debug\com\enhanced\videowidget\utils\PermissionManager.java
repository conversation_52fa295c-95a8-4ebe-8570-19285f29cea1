package com.enhanced.videowidget.utils;

import java.lang.System;

/**
 * Utility class for managing file access permissions
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\u0018\u0000 \u00032\u00020\u0001:\u0001\u0003B\u0005\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0004"}, d2 = {"Lcom/enhanced/videowidget/utils/PermissionManager;", "", "()V", "Companion", "app_debug"})
public final class PermissionManager {
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.utils.PermissionManager.Companion Companion = null;
    public static final int REQUEST_CODE_STORAGE_PERMISSION = 1001;
    public static final int REQUEST_CODE_MANAGE_EXTERNAL_STORAGE = 1002;
    private static final java.lang.String TAG = "PermissionManager";
    
    public PermissionManager() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\b\u001a\u00020\u0007J:\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u00102\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\n0\u0010JG\u0010\u0012\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u00042\u000e\u0010\u0013\u001a\n\u0012\u0006\b\u0001\u0012\u00020\u00070\u00142\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u00102\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\n0\u0010\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010\u0018\u001a\u00020\u00192\u0006\u0010\r\u001a\u00020\u000eJ\u0010\u0010\u001a\u001a\u00020\n2\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\u000e\u0010\u001d\u001a\u00020\n2\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001e\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u001cR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/enhanced/videowidget/utils/PermissionManager$Companion;", "", "()V", "REQUEST_CODE_MANAGE_EXTERNAL_STORAGE", "", "REQUEST_CODE_STORAGE_PERMISSION", "TAG", "", "getPermissionExplanation", "handleActivityResult", "", "requestCode", "resultCode", "context", "Landroid/content/Context;", "onPermissionGranted", "Lkotlin/Function0;", "onPermissionDenied", "handlePermissionResult", "permissions", "", "grantResults", "", "(I[Ljava/lang/String;[ILkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V", "hasStoragePermissions", "", "requestManageExternalStoragePermission", "activity", "Landroid/app/Activity;", "requestStoragePermissions", "shouldShowPermissionRationale", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Check if the app has all necessary storage permissions
         */
        public final boolean hasStoragePermissions(@org.jetbrains.annotations.NotNull
        android.content.Context context) {
            return false;
        }
        
        /**
         * Request storage permissions based on Android version
         */
        public final void requestStoragePermissions(@org.jetbrains.annotations.NotNull
        android.app.Activity activity) {
        }
        
        /**
         * Request MANAGE_EXTERNAL_STORAGE permission for Android 11+
         */
        private final void requestManageExternalStoragePermission(android.app.Activity activity) {
        }
        
        /**
         * Handle permission request results
         */
        public final void handlePermissionResult(int requestCode, @org.jetbrains.annotations.NotNull
        java.lang.String[] permissions, @org.jetbrains.annotations.NotNull
        int[] grantResults, @org.jetbrains.annotations.NotNull
        kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionGranted, @org.jetbrains.annotations.NotNull
        kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionDenied) {
        }
        
        /**
         * Handle activity result for MANAGE_EXTERNAL_STORAGE permission
         */
        public final void handleActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionGranted, @org.jetbrains.annotations.NotNull
        kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionDenied) {
        }
        
        /**
         * Check if we should show permission rationale
         */
        public final boolean shouldShowPermissionRationale(@org.jetbrains.annotations.NotNull
        android.app.Activity activity) {
            return false;
        }
        
        /**
         * Get permission explanation message
         */
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getPermissionExplanation() {
            return null;
        }
    }
}