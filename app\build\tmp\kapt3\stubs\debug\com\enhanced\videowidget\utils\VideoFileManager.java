package com.enhanced.videowidget.utils;

import java.lang.System;

/**
 * Utility class for managing video files in the app's private directory
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \u001b2\u00020\u0001:\u0001\u001bB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001b\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\tJ\u001b\u0010\n\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\f\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u000bJ\u0010\u0010\u0012\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u000bH\u0002J\u0010\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\rH\u0002J\u000e\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0011\u001a\u00020\u000bJ\b\u0010\u0018\u001a\u00020\u0019H\u0002J\u000e\u0010\u001a\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u001c"}, d2 = {"Lcom/enhanced/videowidget/utils/VideoFileManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "cleanupOldVideos", "", "maxFiles", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "copyVideoToPrivateDirectory", "", "videoUri", "Landroid/net/Uri;", "(Landroid/net/Uri;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteVideo", "", "videoPath", "getFileExtension", "fileName", "getFileName", "uri", "getVideoSize", "", "getVideosDirectory", "Ljava/io/File;", "videoExists", "Companion", "app_debug"})
public final class VideoFileManager {
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.utils.VideoFileManager.Companion Companion = null;
    private static final java.lang.String VIDEOS_DIR = "videos";
    private static final java.lang.String TAG = "VideoFileManager";
    
    public VideoFileManager(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super();
    }
    
    /**
     * Get the videos directory in app's private storage
     */
    private final java.io.File getVideosDirectory() {
        return null;
    }
    
    /**
     * Copy a video from URI to app's private directory
     * @param videoUri The URI of the video to copy
     * @return The path to the copied video file, or null if failed
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object copyVideoToPrivateDirectory(@org.jetbrains.annotations.NotNull
    android.net.Uri videoUri, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> continuation) {
        return null;
    }
    
    /**
     * Get the filename from a URI
     */
    private final java.lang.String getFileName(android.net.Uri uri) {
        return null;
    }
    
    /**
     * Get file extension from filename
     */
    private final java.lang.String getFileExtension(java.lang.String fileName) {
        return null;
    }
    
    /**
     * Delete a video file from private directory
     */
    public final boolean deleteVideo(@org.jetbrains.annotations.NotNull
    java.lang.String videoPath) {
        return false;
    }
    
    /**
     * Get the size of a video file
     */
    public final long getVideoSize(@org.jetbrains.annotations.NotNull
    java.lang.String videoPath) {
        return 0L;
    }
    
    /**
     * Check if a video file exists in private directory
     */
    public final boolean videoExists(@org.jetbrains.annotations.NotNull
    java.lang.String videoPath) {
        return false;
    }
    
    /**
     * Clean up old video files to free space
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object cleanupOldVideos(int maxFiles, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/enhanced/videowidget/utils/VideoFileManager$Companion;", "", "()V", "TAG", "", "VIDEOS_DIR", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}