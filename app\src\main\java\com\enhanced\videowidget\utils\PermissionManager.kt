package com.enhanced.videowidget.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * Utility class for managing file access permissions
 */
class PermissionManager {

    companion object {
        const val REQUEST_CODE_STORAGE_PERMISSION = 1001
        const val REQUEST_CODE_MANAGE_EXTERNAL_STORAGE = 1002
        private const val TAG = "PermissionManager"

        /**
         * Check if the app has all necessary storage permissions
         */
        fun hasStoragePermissions(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ - Check for MANAGE_EXTERNAL_STORAGE
                Environment.isExternalStorageManager()
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ - Check for READ_MEDIA_VIDEO
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.READ_MEDIA_VIDEO
                ) == PackageManager.PERMISSION_GRANTED
            } else {
                // Android 12 and below - Check for READ_EXTERNAL_STORAGE
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            }
        }

        /**
         * Request storage permissions based on Android version
         */
        fun requestStoragePermissions(activity: Activity) {
            AppLogger.d(TAG, "Requesting storage permissions for Android ${Build.VERSION.SDK_INT}")
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ - Request MANAGE_EXTERNAL_STORAGE
                requestManageExternalStoragePermission(activity)
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ - Request READ_MEDIA_VIDEO
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.READ_MEDIA_VIDEO),
                    REQUEST_CODE_STORAGE_PERMISSION
                )
            } else {
                // Android 12 and below - Request READ_EXTERNAL_STORAGE
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                    REQUEST_CODE_STORAGE_PERMISSION
                )
            }
        }

        /**
         * Request MANAGE_EXTERNAL_STORAGE permission for Android 11+
         */
        private fun requestManageExternalStoragePermission(activity: Activity) {
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:${activity.packageName}")
                }
                activity.startActivityForResult(intent, REQUEST_CODE_MANAGE_EXTERNAL_STORAGE)
                AppLogger.d(TAG, "Launched MANAGE_EXTERNAL_STORAGE permission request")
            } catch (e: Exception) {
                AppLogger.e(TAG, "Error launching MANAGE_EXTERNAL_STORAGE permission request", e)
                // Fallback to general settings
                try {
                    val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                    activity.startActivityForResult(intent, REQUEST_CODE_MANAGE_EXTERNAL_STORAGE)
                } catch (e2: Exception) {
                    AppLogger.e(TAG, "Error launching fallback permission request", e2)
                }
            }
        }

        /**
         * Handle permission request results
         */
        fun handlePermissionResult(
            requestCode: Int,
            permissions: Array<out String>,
            grantResults: IntArray,
            onPermissionGranted: () -> Unit,
            onPermissionDenied: () -> Unit
        ) {
            when (requestCode) {
                REQUEST_CODE_STORAGE_PERMISSION -> {
                    if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        AppLogger.d(TAG, "Storage permission granted")
                        onPermissionGranted()
                    } else {
                        AppLogger.w(TAG, "Storage permission denied")
                        onPermissionDenied()
                    }
                }
            }
        }

        /**
         * Handle activity result for MANAGE_EXTERNAL_STORAGE permission
         */
        fun handleActivityResult(
            requestCode: Int,
            resultCode: Int,
            context: Context,
            onPermissionGranted: () -> Unit,
            onPermissionDenied: () -> Unit
        ) {
            when (requestCode) {
                REQUEST_CODE_MANAGE_EXTERNAL_STORAGE -> {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        if (Environment.isExternalStorageManager()) {
                            AppLogger.d(TAG, "MANAGE_EXTERNAL_STORAGE permission granted")
                            onPermissionGranted()
                        } else {
                            AppLogger.w(TAG, "MANAGE_EXTERNAL_STORAGE permission denied")
                            onPermissionDenied()
                        }
                    }
                }
            }
        }

        /**
         * Check if we should show permission rationale
         */
        fun shouldShowPermissionRationale(activity: Activity): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.READ_MEDIA_VIDEO
                )
            } else {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                )
            }
        }

        /**
         * Get permission explanation message
         */
        fun getPermissionExplanation(): String {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                "This app needs access to manage external storage to copy and process video files for widgets. Please grant 'All files access' permission in the next screen."
            } else {
                "This app needs storage permission to access and process video files for widgets."
            }
        }
    }
}
