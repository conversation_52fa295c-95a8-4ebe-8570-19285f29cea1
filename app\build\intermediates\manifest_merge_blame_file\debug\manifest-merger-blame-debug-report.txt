1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.enhanced.videowidget"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission
12-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:5-8:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:22-77
14        android:maxSdkVersion="32" />
14-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:8:9-35
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:5-75
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:22-72
16    <uses-permission
16-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:5-13:40
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:22-79
20    <uses-permission android:name="android.permission.INTERNET" />
20-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:5-67
20-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:22-64
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:5-68
21-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:22-65
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:16:5-77
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
23-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:17:5-95
23-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:17:22-92
24
25    <!-- Features -->
26    <uses-feature
26-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:20:5-22:35
27        android:name="android.software.app_widgets"
27-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:21:9-52
28        android:required="true" />
28-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:22:9-32
29
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
30-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
31    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
31-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
31-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
32
33    <permission
33-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
34        android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
38
39    <application
39-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:24:5-86:19
40        android:allowBackup="true"
40-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:25:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:26:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:27:9-54
46        android:icon="@mipmap/ic_launcher"
46-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:28:9-43
47        android:label="@string/app_name"
47-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:29:9-41
48        android:theme="@style/Theme.EnhancedVideoWidget" >
48-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:30:9-57
49
50        <!-- Main Activity -->
51        <activity
51-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:34:9-42:20
52            android:name="com.enhanced.videowidget.ui.MainActivity"
52-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:35:13-44
53            android:exported="true"
53-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:36:13-36
54            android:theme="@style/Theme.EnhancedVideoWidget" >
54-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:37:13-61
55            <intent-filter>
55-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:38:13-41:29
56                <action android:name="android.intent.action.MAIN" />
56-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:39:17-69
56-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:39:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:40:17-77
58-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:40:27-74
59            </intent-filter>
60        </activity>
61
62        <!-- Widget Configuration Activity -->
63        <activity
63-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:45:9-52:20
64            android:name="com.enhanced.videowidget.ui.WidgetConfigurationActivity"
64-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:46:13-59
65            android:exported="false"
65-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:47:13-37
66            android:theme="@style/Theme.EnhancedVideoWidget.NoActionBar" >
66-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:48:13-73
67            <intent-filter>
67-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:49:13-51:29
68                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
68-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:50:17-87
68-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:50:25-84
69            </intent-filter>
70        </activity>
71
72        <!-- Enhanced Video Widget Provider -->
73        <receiver
73-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:55:9-67:20
74            android:name="com.enhanced.videowidget.widget.EnhancedVideoWidget"
74-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:56:13-55
75            android:exported="true" >
75-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:13-36
76            <intent-filter>
76-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:13-63:29
77                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
77-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:17-84
77-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:25-81
78                <action android:name="com.enhanced.videowidget.ACTION_WIDGET_TAP" />
78-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:17-85
78-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:25-82
79                <action android:name="com.enhanced.videowidget.ACTION_PLAY_PAUSE" />
79-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:61:17-85
79-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:61:25-82
80                <action android:name="com.enhanced.videowidget.ACTION_SETTINGS" />
80-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:62:17-83
80-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:62:25-80
81            </intent-filter>
82
83            <meta-data
83-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:64:13-66:70
84                android:name="android.appwidget.provider"
84-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:65:17-58
85                android:resource="@xml/enhanced_video_widget_info" />
85-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:66:17-67
86        </receiver>
87
88        <!-- Widget Update Service -->
89        <service
89-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:70:9-73:56
90            android:name="com.enhanced.videowidget.service.WidgetUpdateService"
90-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:71:13-56
91            android:exported="false"
91-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:72:13-37
92            android:foregroundServiceType="dataSync" />
92-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:73:13-53
93
94        <!-- Work Manager -->
95        <provider
96            android:name="androidx.startup.InitializationProvider"
96-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:77:13-67
97            android:authorities="com.enhanced.videowidget.androidx-startup"
97-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:78:13-68
98            android:exported="false" >
98-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:79:13-37
99            <meta-data
99-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:81:13-83:52
100                android:name="androidx.work.WorkManagerInitializer"
100-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:82:17-68
101                android:value="androidx.startup" />
101-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:83:17-49
102            <meta-data
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
103                android:name="androidx.emoji2.text.EmojiCompatInitializer"
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
104                android:value="androidx.startup" />
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
105            <meta-data
105-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
106-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
107                android:value="androidx.startup" />
107-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
108            <meta-data
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
110                android:value="androidx.startup" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
111        </provider>
112
113        <service
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
114            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
115            android:directBootAware="false"
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
116            android:enabled="@bool/enable_system_alarm_service_default"
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
117            android:exported="false" />
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
118        <service
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
119            android:name="androidx.work.impl.background.systemjob.SystemJobService"
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
120            android:directBootAware="false"
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
121            android:enabled="@bool/enable_system_job_service_default"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
122            android:exported="true"
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
123            android:permission="android.permission.BIND_JOB_SERVICE" />
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
124        <service
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
125            android:name="androidx.work.impl.foreground.SystemForegroundService"
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
126            android:directBootAware="false"
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
127            android:enabled="@bool/enable_system_foreground_service_default"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
128            android:exported="false" />
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
129
130        <receiver
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
131            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
132            android:directBootAware="false"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
133            android:enabled="true"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
134            android:exported="false" />
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
135        <receiver
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
136            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
137            android:directBootAware="false"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
138            android:enabled="false"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
139            android:exported="false" >
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
140            <intent-filter>
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
141                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
142                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
143            </intent-filter>
144        </receiver>
145        <receiver
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
146            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
148            android:enabled="false"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
149            android:exported="false" >
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
150            <intent-filter>
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
151                <action android:name="android.intent.action.BATTERY_OKAY" />
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
152                <action android:name="android.intent.action.BATTERY_LOW" />
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
153            </intent-filter>
154        </receiver>
155        <receiver
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
156            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
158            android:enabled="false"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
159            android:exported="false" >
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
160            <intent-filter>
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
161                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
162                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
163            </intent-filter>
164        </receiver>
165        <receiver
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
166            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
168            android:enabled="false"
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
169            android:exported="false" >
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
170            <intent-filter>
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
171                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
172            </intent-filter>
173        </receiver>
174        <receiver
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
175            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
176            android:directBootAware="false"
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
177            android:enabled="false"
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
178            android:exported="false" >
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
179            <intent-filter>
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
180                <action android:name="android.intent.action.BOOT_COMPLETED" />
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
181                <action android:name="android.intent.action.TIME_SET" />
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
182                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
183            </intent-filter>
184        </receiver>
185        <receiver
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
186            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
188            android:enabled="@bool/enable_system_alarm_service_default"
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
190            <intent-filter>
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
191                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
192            </intent-filter>
193        </receiver>
194        <receiver
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
195            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
196            android:directBootAware="false"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
197            android:enabled="true"
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
198            android:exported="true"
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
199            android:permission="android.permission.DUMP" >
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
200            <intent-filter>
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
201                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
202            </intent-filter>
203        </receiver>
204
205        <service
205-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
206            android:name="androidx.room.MultiInstanceInvalidationService"
206-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
207            android:directBootAware="true"
207-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
208            android:exported="false" />
208-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
209
210        <receiver
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
211            android:name="androidx.profileinstaller.ProfileInstallReceiver"
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
212            android:directBootAware="false"
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
213            android:enabled="true"
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
214            android:exported="true"
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
215            android:permission="android.permission.DUMP" >
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
217                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
220                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
221            </intent-filter>
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
223                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
224            </intent-filter>
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
226                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
227            </intent-filter>
228        </receiver>
229    </application>
230
231</manifest>
