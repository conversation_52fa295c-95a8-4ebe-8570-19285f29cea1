1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.enhanced.videowidget"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission
12-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:5-8:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:22-77
14        android:maxSdkVersion="32" />
14-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:8:9-35
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:5-75
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:22-72
16    <uses-permission
16-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:5-13:40
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:22-79
20    <uses-permission android:name="android.permission.INTERNET" />
20-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:5-67
20-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:22-64
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:5-68
21-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:22-65
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:16:5-77
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
23-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:17:5-95
23-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:17:22-92
24
25    <!-- Features -->
26    <uses-feature
26-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:20:5-22:35
27        android:name="android.software.app_widgets"
27-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:21:9-52
28        android:required="true" />
28-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:22:9-32
29
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
30-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
31    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
31-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
31-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
32
33    <permission
33-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
34        android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
38
39    <application
39-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:24:5-97:19
40        android:allowBackup="true"
40-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:25:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:26:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:27:9-54
46        android:icon="@mipmap/ic_launcher"
46-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:28:9-43
47        android:label="@string/app_name"
47-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:29:9-41
48        android:theme="@style/Theme.EnhancedVideoWidget" >
48-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:30:9-57
49
50        <!-- Main Activity -->
51        <activity
51-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:34:9-42:20
52            android:name="com.enhanced.videowidget.ui.MainActivity"
52-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:35:13-44
53            android:exported="true"
53-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:36:13-36
54            android:theme="@style/Theme.EnhancedVideoWidget.NoActionBar" >
54-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:37:13-73
55            <intent-filter>
55-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:38:13-41:29
56                <action android:name="android.intent.action.MAIN" />
56-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:39:17-69
56-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:39:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:40:17-77
58-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:40:27-74
59            </intent-filter>
60        </activity>
61
62        <!-- Widget Configuration Activity -->
63        <activity
63-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:45:9-52:20
64            android:name="com.enhanced.videowidget.ui.WidgetConfigurationActivity"
64-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:46:13-59
65            android:exported="false"
65-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:47:13-37
66            android:theme="@style/Theme.EnhancedVideoWidget.NoActionBar" >
66-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:48:13-73
67            <intent-filter>
67-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:49:13-51:29
68                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
68-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:50:17-87
68-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:50:25-84
69            </intent-filter>
70        </activity>
71
72        <!-- Video Widget Manager Activity -->
73        <activity
73-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:55:9-63:20
74            android:name="com.enhanced.videowidget.ui.VideoWidgetManagerActivity"
74-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:56:13-58
75            android:exported="false"
75-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:13-37
76            android:parentActivityName="com.enhanced.videowidget.ui.MainActivity"
76-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:13-58
77            android:theme="@style/Theme.EnhancedVideoWidget" >
77-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:13-61
78            <meta-data
78-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:13-62:52
79                android:name="android.support.PARENT_ACTIVITY"
79-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:61:17-63
80                android:value=".ui.MainActivity" />
80-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:62:17-49
81        </activity>
82
83        <!-- Enhanced Video Widget Provider -->
84        <receiver
84-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:66:9-78:20
85            android:name="com.enhanced.videowidget.widget.EnhancedVideoWidget"
85-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:67:13-55
86            android:exported="true" >
86-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:68:13-36
87            <intent-filter>
87-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:69:13-74:29
88                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
88-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:70:17-84
88-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:70:25-81
89                <action android:name="com.enhanced.videowidget.ACTION_WIDGET_TAP" />
89-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:71:17-85
89-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:71:25-82
90                <action android:name="com.enhanced.videowidget.ACTION_PLAY_PAUSE" />
90-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:72:17-85
90-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:72:25-82
91                <action android:name="com.enhanced.videowidget.ACTION_SETTINGS" />
91-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:73:17-83
91-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:73:25-80
92            </intent-filter>
93
94            <meta-data
94-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:75:13-77:70
95                android:name="android.appwidget.provider"
95-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:76:17-58
96                android:resource="@xml/enhanced_video_widget_info" />
96-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:77:17-67
97        </receiver>
98
99        <!-- Widget Update Service -->
100        <service
100-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:81:9-84:56
101            android:name="com.enhanced.videowidget.service.WidgetUpdateService"
101-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:82:13-56
102            android:exported="false"
102-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:83:13-37
103            android:foregroundServiceType="dataSync" />
103-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:84:13-53
104
105        <!-- Work Manager -->
106        <provider
107            android:name="androidx.startup.InitializationProvider"
107-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:88:13-67
108            android:authorities="com.enhanced.videowidget.androidx-startup"
108-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:89:13-68
109            android:exported="false" >
109-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:90:13-37
110            <meta-data
110-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:92:13-94:52
111                android:name="androidx.work.WorkManagerInitializer"
111-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:93:17-68
112                android:value="androidx.startup" />
112-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:94:17-49
113            <meta-data
113-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.emoji2.text.EmojiCompatInitializer"
114-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
115                android:value="androidx.startup" />
115-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
117-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
118                android:value="androidx.startup" />
118-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
119            <meta-data
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
121                android:value="androidx.startup" />
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
122        </provider>
123
124        <service
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
125            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
126            android:directBootAware="false"
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
127            android:enabled="@bool/enable_system_alarm_service_default"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
128            android:exported="false" />
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
129        <service
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
130            android:name="androidx.work.impl.background.systemjob.SystemJobService"
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
131            android:directBootAware="false"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
132            android:enabled="@bool/enable_system_job_service_default"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
133            android:exported="true"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
134            android:permission="android.permission.BIND_JOB_SERVICE" />
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
135        <service
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
136            android:name="androidx.work.impl.foreground.SystemForegroundService"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
137            android:directBootAware="false"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
138            android:enabled="@bool/enable_system_foreground_service_default"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
139            android:exported="false" />
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
140
141        <receiver
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
142            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
144            android:enabled="true"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
145            android:exported="false" />
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
146        <receiver
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
147            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
148            android:directBootAware="false"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
149            android:enabled="false"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
150            android:exported="false" >
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
151            <intent-filter>
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
152                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
153                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
154            </intent-filter>
155        </receiver>
156        <receiver
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
157            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
159            android:enabled="false"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
160            android:exported="false" >
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
161            <intent-filter>
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
162                <action android:name="android.intent.action.BATTERY_OKAY" />
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
163                <action android:name="android.intent.action.BATTERY_LOW" />
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
164            </intent-filter>
165        </receiver>
166        <receiver
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
169            android:enabled="false"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
170            android:exported="false" >
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
171            <intent-filter>
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
172                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
173                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
182                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
183            </intent-filter>
184        </receiver>
185        <receiver
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
186            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
188            android:enabled="false"
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
190            <intent-filter>
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
191                <action android:name="android.intent.action.BOOT_COMPLETED" />
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
192                <action android:name="android.intent.action.TIME_SET" />
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
193                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
194            </intent-filter>
195        </receiver>
196        <receiver
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
197            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
198            android:directBootAware="false"
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
199            android:enabled="@bool/enable_system_alarm_service_default"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
200            android:exported="false" >
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
201            <intent-filter>
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
202                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
203            </intent-filter>
204        </receiver>
205        <receiver
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
206            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
208            android:enabled="true"
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
209            android:exported="true"
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
210            android:permission="android.permission.DUMP" >
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
211            <intent-filter>
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
212                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
213            </intent-filter>
214        </receiver>
215
216        <service
216-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
217            android:name="androidx.room.MultiInstanceInvalidationService"
217-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
218            android:directBootAware="true"
218-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
219            android:exported="false" />
219-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
220
221        <receiver
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
222            android:name="androidx.profileinstaller.ProfileInstallReceiver"
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
223            android:directBootAware="false"
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
224            android:enabled="true"
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
225            android:exported="true"
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
226            android:permission="android.permission.DUMP" >
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
228                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
229            </intent-filter>
230            <intent-filter>
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
231                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
232            </intent-filter>
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
234                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
237                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
238            </intent-filter>
239        </receiver>
240    </application>
241
242</manifest>
