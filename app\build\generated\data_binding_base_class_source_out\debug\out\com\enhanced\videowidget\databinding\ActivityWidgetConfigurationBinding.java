// Generated by view binder compiler. Do not edit!
package com.enhanced.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.enhanced.videowidget.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWidgetConfigurationBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonCancel;

  @NonNull
  public final MaterialButton buttonCreateWidget;

  @NonNull
  public final MaterialButton buttonSelectVideo;

  @NonNull
  public final TextInputEditText editTextWidgetName;

  @NonNull
  public final ImageView imageViewPreview;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final LinearLayout progressContainer;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final TextView textViewSelectedVideo;

  @NonNull
  public final TextView textViewVideoInfo;

  @NonNull
  public final Toolbar toolbar;

  private ActivityWidgetConfigurationBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonCancel, @NonNull MaterialButton buttonCreateWidget,
      @NonNull MaterialButton buttonSelectVideo, @NonNull TextInputEditText editTextWidgetName,
      @NonNull ImageView imageViewPreview, @NonNull ProgressBar progressBar,
      @NonNull LinearLayout progressContainer, @NonNull TextView progressText,
      @NonNull TextView textViewSelectedVideo, @NonNull TextView textViewVideoInfo,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonCreateWidget = buttonCreateWidget;
    this.buttonSelectVideo = buttonSelectVideo;
    this.editTextWidgetName = editTextWidgetName;
    this.imageViewPreview = imageViewPreview;
    this.progressBar = progressBar;
    this.progressContainer = progressContainer;
    this.progressText = progressText;
    this.textViewSelectedVideo = textViewSelectedVideo;
    this.textViewVideoInfo = textViewVideoInfo;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityWidgetConfigurationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWidgetConfigurationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_widget_configuration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWidgetConfigurationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonCancel;
      MaterialButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.buttonCreateWidget;
      MaterialButton buttonCreateWidget = ViewBindings.findChildViewById(rootView, id);
      if (buttonCreateWidget == null) {
        break missingId;
      }

      id = R.id.buttonSelectVideo;
      MaterialButton buttonSelectVideo = ViewBindings.findChildViewById(rootView, id);
      if (buttonSelectVideo == null) {
        break missingId;
      }

      id = R.id.editTextWidgetName;
      TextInputEditText editTextWidgetName = ViewBindings.findChildViewById(rootView, id);
      if (editTextWidgetName == null) {
        break missingId;
      }

      id = R.id.imageViewPreview;
      ImageView imageViewPreview = ViewBindings.findChildViewById(rootView, id);
      if (imageViewPreview == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progressContainer;
      LinearLayout progressContainer = ViewBindings.findChildViewById(rootView, id);
      if (progressContainer == null) {
        break missingId;
      }

      id = R.id.progressText;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.textViewSelectedVideo;
      TextView textViewSelectedVideo = ViewBindings.findChildViewById(rootView, id);
      if (textViewSelectedVideo == null) {
        break missingId;
      }

      id = R.id.textViewVideoInfo;
      TextView textViewVideoInfo = ViewBindings.findChildViewById(rootView, id);
      if (textViewVideoInfo == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityWidgetConfigurationBinding((CoordinatorLayout) rootView, buttonCancel,
          buttonCreateWidget, buttonSelectVideo, editTextWidgetName, imageViewPreview, progressBar,
          progressContainer, progressText, textViewSelectedVideo, textViewVideoInfo, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
